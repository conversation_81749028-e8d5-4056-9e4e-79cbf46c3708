import { MessagingService } from '@/lib/service/messagingService'
import { MessageType } from '@/types'
import { Platform } from '@/types/database_entity'
import { DOMUtils } from './DOMUtils'

// 前向声明，避免循环依赖
interface BaseAIAdapter {
  getElement(type: 'inputField' | 'inputContainer', forceRefresh?: boolean): HTMLElement | null
  addEventListener(event: string, callback: (data: any) => void): void
  removeEventListener(event: string, callback: (data: any) => void): void
  getCurrentInput(): string
  getPlatformName(): string
}

/**
 * 存档按钮管理类
 * 重构后通过 adapter 实例获取页面元素，不直接操作 DOM
 */
export class ArchiveButton {
  private button: HTMLElement | null = null
  private currentPromptId: string = ''
  private archivedPromptIds: Set<string> = new Set()
  private adapter: BaseAIAdapter | null = null

  constructor(adapter?: BaseAIAdapter) {
    this.adapter = adapter || null
    this.generateNewPromptId()

    if (this.adapter) {
      this.setupEventListeners()
    }
  }

  /**
   * 设置适配器实例
   * @param adapter 适配器实例
   */
  setAdapter(adapter: BaseAIAdapter): void {
    this.adapter = adapter
    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.adapter) return

    // 监听输入变化事件
    this.adapter.addEventListener('input-changed', (data) => {
      this.updateArchiveButtonState(data.value)
    })

    // 监听输入聚焦事件
    this.adapter.addEventListener('input-focused', () => {
      this.repositionButton()
    })

    // 监听页面变化事件
    this.adapter.addEventListener('page-changed', () => {
      this.repositionButton()
    })
  }

  /**
   * 添加存档按钮
   * 重构后通过 adapter 获取元素
   */
  async addArchiveButton(legacySelectors?: { inputField: string }): Promise<void> {
    if (!this.adapter) {
      console.warn('【EchoSync】ArchiveButton: No adapter instance available')
      return
    }

    // 通过 adapter 获取输入框元素
    const inputElement = this.adapter.getElement('inputField')
    if (!inputElement) {
      console.warn('【EchoSync】ArchiveButton: No input element found')
      return
    }

    // 通过 adapter 获取输入容器
    let inputContainer = this.adapter.getElement('inputContainer')
    if (!inputContainer) {
      // 如果没有找到输入容器，使用输入框的父容器
      inputContainer = DOMUtils.findInputContainer(inputElement)
    }

    if (!inputContainer) {
      console.warn('【EchoSync】ArchiveButton: No input container found')
      return
    }

    // 创建存档按钮
    this.button = document.createElement('div')
    this.button.className = 'echosync-archive-button'
    this.button.innerHTML = '📁'
    this.button.title = '存档当前提示词'

    // 设置按钮样式
    this.button.style.cssText = `
      position: fixed;
      width: 25px;
      height: 25px;
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10001;
      box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: white;
      font-size: 12px;
      opacity: 0;
      transform: scale(0.8);
      pointer-events: none;
      user-select: none;
    `

    // 定位按钮
    this.positionArchiveButton(inputContainer)

    // 添加点击事件
    this.button.addEventListener('click', () => {
      document.dispatchEvent(new CustomEvent('echosync:archive-current-prompt'))
    })

    document.body.appendChild(this.button)
    console.log('【EchoSync】Archive button created and positioned')
  }

  /**
   * 重新定位按钮
   * 通过 adapter 获取最新的元素位置
   */
  private repositionButton(): void {
    if (!this.button || !this.adapter) return

    // 通过 adapter 获取输入容器
    let inputContainer = this.adapter.getElement('inputContainer')
    if (!inputContainer) {
      // 如果没有找到输入容器，尝试获取输入框
      const inputElement = this.adapter.getElement('inputField')
      if (inputElement) {
        inputContainer = DOMUtils.findInputContainer(inputElement)
      }
    }

    if (inputContainer) {
      this.positionArchiveButton(inputContainer)
    }
  }

  /**
   * 定位存档按钮到输入框右侧，底部对齐
   */
  private positionArchiveButton(inputContainer: HTMLElement): void {
    if (!this.button) return

    const updatePosition = () => {
      const rect = inputContainer.getBoundingClientRect()
      const buttonSize = 25
      const margin = 8

      // 直接定位在输入框右侧，底部对齐
      let left = rect.right + margin
      let top = rect.bottom - buttonSize

      // 确保按钮不会超出视窗边界
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      left = Math.max(margin, Math.min(left, windowWidth - buttonSize - margin))
      top = Math.max(margin, Math.min(top, windowHeight - buttonSize - margin))

      this.button!.style.left = `${left}px`
      this.button!.style.top = `${top}px`
    }

    updatePosition()

    // 监听窗口大小变化和滚动，使用防抖优化性能
    let resizeTimeout: NodeJS.Timeout
    const debouncedUpdate = () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(updatePosition, 100)
    }

    window.addEventListener('resize', debouncedUpdate)
    window.addEventListener('scroll', debouncedUpdate)

    // 监听输入框位置变化
    const resizeObserver = new ResizeObserver(debouncedUpdate)
    resizeObserver.observe(inputContainer)

    // 监听输入框父容器的变化（处理动态布局）
    if (inputContainer.parentElement) {
      resizeObserver.observe(inputContainer.parentElement)
    }
  }



  /**
   * 更新存档按钮状态
   * 重构后可以通过 adapter 获取当前输入内容
   */
  updateArchiveButtonState(inputValue?: string): void {
    if (!this.button) return

    // 如果没有传入 inputValue，通过 adapter 获取
    let content = inputValue
    if (!content && this.adapter) {
      content = this.adapter.getCurrentInput()
    }

    const hasContent = (content || '').trim().length > 0
    const isArchived = this.archivedPromptIds.has(this.currentPromptId)

    if (hasContent && !isArchived) {
      // 显示按钮并添加发光效果
      this.showArchiveButtonWithGlow()
    } else if (isArchived) {
      // 显示已存档状态
      this.showArchivedState()
    } else {
      // 隐藏按钮
      this.hideArchiveButton()
    }
  }

  /**
   * 显示存档按钮并添加发光效果
   */
  private showArchiveButtonWithGlow(): void {
    if (!this.button) return

    this.button.style.opacity = '1'
    this.button.style.transform = 'scale(1)'
    this.button.style.pointerEvents = 'auto'

    // 添加贝塞尔曲线发光动画
    this.button.style.animation = 'echosync-glow 2s ease-in-out infinite'
    this.button.classList.remove('archived')

    // 添加发光动画样式
    if (!document.getElementById('echosync-glow-styles')) {
      const style = document.createElement('style')
      style.id = 'echosync-glow-styles'
      style.textContent = `
        @keyframes echosync-glow {
          0%, 100% {
            box-shadow: 0 4px 25px rgba(139, 92, 246, 0.4);
            transform: scale(1);
          }
          50% {
            box-shadow: 0 4px 35px rgba(139, 92, 246, 0.8), 0 0 30px rgba(139, 92, 246, 0.6);
            transform: scale(1.05);
          }
        }
      `
      document.head.appendChild(style)
    }
  }

  /**
   * 显示已存档状态
   */
  private showArchivedState(): void {
    if (!this.button) return

    this.button.style.opacity = '1'
    this.button.style.transform = 'scale(1)'
    this.button.style.pointerEvents = 'none'
    this.button.style.animation = 'none'
    this.button.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
    this.button.innerHTML = '✓'
    this.button.classList.add('archived')
  }

  /**
   * 隐藏存档按钮
   */
  private hideArchiveButton(): void {
    if (!this.button) return

    this.button.style.opacity = '0'
    this.button.style.transform = 'scale(0.8)'
    this.button.style.pointerEvents = 'none'
    this.button.style.animation = 'none'
  }

  /**
   * 自动存档提示词（发送时调用）
   * 重构后通过 MessagingService 与 Background 通信
   */
  async autoArchivePrompt(promptContent: string, platformName?: string): Promise<void> {
    try {
      if (!this.adapter) {
        console.warn('【EchoSync】ArchiveButton: No adapter instance for auto archive')
        return
      }

      const platform = platformName || this.adapter.getPlatformName()
      if (!platform) {
        console.warn('【EchoSync】ArchiveButton: Cannot identify platform, skipping auto archive')
        return
      }

      // 生成新的提示词ID
      this.generateNewPromptId()
      console.log('【EchoSync】Generated new prompt ID for auto archive:', this.currentPromptId)

      // 通过 MessagingService 发送存档请求到 Background
      const archiveData = {
        chat_prompt: promptContent,
        chat_uid: this.currentPromptId,
        platform_name: platform,
        create_time: Date.now()
      }

      try {
        const result = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_CREATE, archiveData)

        if (result.success) {
          // 标记为已存档
          this.archivedPromptIds.add(this.currentPromptId)
          console.log('【EchoSync】Prompt auto-archived successfully:', result.data)
        } else {
          console.error('【EchoSync】Auto archive failed:', result.error)
        }
      } catch (error) {
        console.error('【EchoSync】Auto archive request failed:', error)
      }
    } catch (error) {
      console.error('【EchoSync】Auto archive prompt error:', error)
    }
  }

  /**
   * 存档当前提示词
   * 重构后通过 adapter 获取输入内容和平台信息
   */
  async archiveCurrentPrompt(
    legacyGetCurrentInput?: () => string,
    legacyCurrentPlatform?: Platform | null,
    legacyShowNotification?: (message: string, type: string) => void
  ): Promise<void> {
    if (!this.adapter) {
      console.error('【EchoSync】ArchiveButton: No adapter instance available for archiving')
      return
    }

    // 通过 adapter 获取当前输入内容
    const currentPrompt = this.adapter.getCurrentInput()
    if (!currentPrompt || currentPrompt.trim().length === 0) {
      console.warn('【EchoSync】ArchiveButton: Input is empty, cannot archive')
      return
    }

    try {
      // 获取平台名称
      const platformName = this.adapter.getPlatformName()
      if (!platformName) {
        console.error('【EchoSync】ArchiveButton: Cannot identify current platform')
        return
      }

      // 生成新的 prompt ID
      this.generateNewPromptId()
      console.log('【EchoSync】Generated new prompt ID for archiving:', this.currentPromptId)

      // 通过 MessagingService 发送存档请求到 Background
      const archiveData = {
        chat_prompt: currentPrompt,
        chat_uid: this.currentPromptId,
        platform_name: platformName,
        create_time: Date.now()
      }

      try {
        const result = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_CREATE, archiveData)

        if (result.success) {
          // 标记为已存档
          this.archivedPromptIds.add(this.currentPromptId)

          // 更新按钮状态
          this.showArchivedState()

          // 显示存档动画
          this.showArchiveAnimation()

          console.log('【EchoSync】Prompt archived successfully:', result.data)
        } else {
          console.error('【EchoSync】Archive failed:', result.error)
        }
      } catch (error) {
        console.error('【EchoSync】Archive request failed:', error)
      }
    } catch (error) {
      console.error('【EchoSync】Archive prompt error:', error)
    }
  }

  /**
   * 显示存档动画
   */
  private showArchiveAnimation(): void {
    if (!this.button) return

    // 创建飞行动画元素
    const flyingIcon = this.button.cloneNode(true) as HTMLElement
    flyingIcon.style.cssText = `
      position: fixed;
      z-index: 10001;
      pointer-events: none;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    `

    const buttonRect = this.button.getBoundingClientRect()
    flyingIcon.style.left = `${buttonRect.left}px`
    flyingIcon.style.top = `${buttonRect.top}px`

    document.body.appendChild(flyingIcon)

    // 动画到右上角
    setTimeout(() => {
      flyingIcon.style.left = `${window.innerWidth - 100}px`
      flyingIcon.style.top = '20px'
      flyingIcon.style.transform = 'scale(0.3)'
      flyingIcon.style.opacity = '0'
    }, 50)

    // 清理动画元素
    setTimeout(() => {
      document.body.removeChild(flyingIcon)
    }, 650)
  }

  /**
   * 生成新的提示词ID
   */
  generateNewPromptId(): void {
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)
    this.currentPromptId = `prompt-${timestamp}-${randomStr}`
  }

  /**
   * 获取当前提示词ID
   */
  getCurrentPromptId(): string {
    return this.currentPromptId
  }

  /**
   * 设置当前提示词ID（用于跨平台共享）
   */
  setCurrentPromptId(chatUid: string): void {
    this.currentPromptId = chatUid
  }

  /**
   * 标记为已存档状态（用于跨平台共享）
   */
  markAsArchived(): void {
    this.archivedPromptIds.add(this.currentPromptId)
    this.showArchivedState()
  }

  /**
   * 获取已存档的提示词ID集合
   */
  getArchivedPromptIds(): Set<string> {
    return this.archivedPromptIds
  }

  /**
   * 获取按钮元素
   */
  getButton(): HTMLElement | null {
    return this.button
  }

  /**
   * 销毁
   */
  destroy(): void {
    if (this.button) {
      this.button.remove()
      this.button = null
    }
  }
}
