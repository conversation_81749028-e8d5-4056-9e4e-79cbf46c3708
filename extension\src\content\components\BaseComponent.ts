/**
 * 基础UI组件抽象类
 * 定义所有UI组件的基本接口和生命周期管理
 */

import { 
  IComponent, 
  ComponentConfig, 
  ComponentLifecycleState 
} from '../types/components'
import { ILogger } from '../types/utils'

/**
 * 基础组件抽象类
 * 提供组件的基本功能和生命周期管理
 */
export abstract class BaseComponent implements IComponent {
  /** 组件ID */
  public readonly id: string
  /** 组件名称 */
  public readonly name: string
  /** 组件配置 */
  protected readonly config: ComponentConfig
  /** 生命周期状态 */
  protected _state: ComponentLifecycleState = ComponentLifecycleState.UNINITIALIZED
  /** DOM元素 */
  protected _element: HTMLElement | null = null
  /** 是否可见 */
  protected _isVisible: boolean = false
  /** 日志器 */
  protected logger?: ILogger
  /** 事件监听器清理函数 */
  protected cleanupFunctions: (() => void)[] = []

  constructor(config: ComponentConfig = {}) {
    this.id = config.id || this.generateId()
    this.name = config.name || this.constructor.name
    this.config = { enabled: true, debug: false, ...config }
    
    if (this.config.debug) {
      console.log(`[${this.name}] Component created with ID: ${this.id}`)
    }
  }

  /**
   * 获取生命周期状态
   */
  get state(): ComponentLifecycleState {
    return this._state
  }

  /**
   * 获取是否可见
   */
  get isVisible(): boolean {
    return this._isVisible && this._element !== null && this.isElementVisible(this._element)
  }

  /**
   * 获取DOM元素
   */
  get element(): HTMLElement | null {
    return this._element
  }

  /**
   * 渲染组件（抽象方法，子类必须实现）
   */
  abstract render(): HTMLElement

  /**
   * 显示组件
   */
  show(): void {
    if (this._state === ComponentLifecycleState.DESTROYED) {
      throw new Error(`[${this.name}] Cannot show destroyed component`)
    }

    if (!this._element) {
      this._element = this.render()
      this._state = ComponentLifecycleState.INITIALIZED
    }

    if (this._element && !this._isVisible) {
      this._element.style.display = ''
      this._isVisible = true
      this.onShow()
      
      if (this.config.debug) {
        console.log(`[${this.name}] Component shown`)
      }
    }
  }

  /**
   * 隐藏组件
   */
  hide(): void {
    if (this._element && this._isVisible) {
      this._element.style.display = 'none'
      this._isVisible = false
      this.onHide()
      
      if (this.config.debug) {
        console.log(`[${this.name}] Component hidden`)
      }
    }
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    if (this._state === ComponentLifecycleState.DESTROYED) {
      return
    }

    this._state = ComponentLifecycleState.DESTROYING

    try {
      // 清理事件监听器
      this.cleanupEventListeners()
      
      // 调用子类的清理方法
      this.onDestroy()
      
      // 移除DOM元素
      if (this._element && this._element.parentNode) {
        this._element.parentNode.removeChild(this._element)
      }
      
      this._element = null
      this._isVisible = false
      this._state = ComponentLifecycleState.DESTROYED
      
      if (this.config.debug) {
        console.log(`[${this.name}] Component destroyed`)
      }
    } catch (error) {
      console.error(`[${this.name}] Error during component destruction:`, error)
      this._state = ComponentLifecycleState.DESTROYED
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (this._state === ComponentLifecycleState.DESTROYED) {
      throw new Error(`[${this.name}] Cannot update destroyed component`)
    }

    try {
      this.onUpdate(data)
      
      if (this.config.debug) {
        console.log(`[${this.name}] Component updated`, data)
      }
    } catch (error) {
      console.error(`[${this.name}] Error during component update:`, error)
    }
  }

  /**
   * 获取组件状态
   */
  getState(): ComponentLifecycleState {
    return this._state
  }

  /**
   * 设置日志器
   */
  setLogger(logger: ILogger): void {
    this.logger = logger
  }

  /**
   * 添加事件监听器（自动管理清理）
   */
  protected addEventListener<K extends keyof HTMLElementEventMap>(
    element: HTMLElement,
    type: K,
    listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any,
    options?: boolean | AddEventListenerOptions
  ): void {
    element.addEventListener(type, listener, options)
    
    // 添加清理函数
    this.cleanupFunctions.push(() => {
      element.removeEventListener(type, listener, options)
    })
  }

  /**
   * 添加全局事件监听器（自动管理清理）
   */
  protected addGlobalEventListener<K extends keyof WindowEventMap>(
    type: K,
    listener: (this: Window, ev: WindowEventMap[K]) => any,
    options?: boolean | AddEventListenerOptions
  ): void {
    window.addEventListener(type, listener, options)
    
    // 添加清理函数
    this.cleanupFunctions.push(() => {
      window.removeEventListener(type, listener, options)
    })
  }

  /**
   * 创建DOM元素的辅助方法
   */
  protected createElement<K extends keyof HTMLElementTagNameMap>(
    tagName: K,
    attributes?: Record<string, string>,
    styles?: Record<string, string>,
    textContent?: string
  ): HTMLElementTagNameMap[K] {
    const element = document.createElement(tagName)
    
    // 设置属性
    if (attributes) {
      Object.entries(attributes).forEach(([key, value]) => {
        element.setAttribute(key, value)
      })
    }
    
    // 设置样式
    if (styles) {
      Object.entries(styles).forEach(([key, value]) => {
        ;(element.style as any)[key] = value
      })
    }
    
    // 设置文本内容
    if (textContent) {
      element.textContent = textContent
    }
    
    return element
  }

  /**
   * 检查元素是否可见
   */
  protected isElementVisible(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect()
    const style = window.getComputedStyle(element)
    
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0'
    )
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `component_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 清理所有事件监听器
   */
  private cleanupEventListeners(): void {
    this.cleanupFunctions.forEach(cleanup => {
      try {
        cleanup()
      } catch (error) {
        console.error(`[${this.name}] Error during event listener cleanup:`, error)
      }
    })
    this.cleanupFunctions = []
  }

  // 生命周期钩子方法（子类可重写）

  /**
   * 组件显示时调用
   */
  protected onShow(): void {
    // 子类可重写
  }

  /**
   * 组件隐藏时调用
   */
  protected onHide(): void {
    // 子类可重写
  }

  /**
   * 组件更新时调用
   */
  protected onUpdate(data?: any): void {
    // 子类可重写
  }

  /**
   * 组件销毁时调用
   */
  protected onDestroy(): void {
    // 子类可重写
  }

  /**
   * 应用自定义样式
   */
  protected applyCustomStyles(element: HTMLElement): void {
    if (this.config.customStyles) {
      Object.entries(this.config.customStyles).forEach(([property, value]) => {
        ;(element.style as any)[property] = value
      })
    }
  }

  /**
   * 应用自定义属性
   */
  protected applyCustomAttributes(element: HTMLElement): void {
    if (this.config.customAttributes) {
      Object.entries(this.config.customAttributes).forEach(([name, value]) => {
        element.setAttribute(name, value)
      })
    }
  }

  /**
   * 记录日志
   */
  protected log(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: any): void {
    const fullMessage = `[${this.name}] ${message}`
    
    if (this.logger) {
      this.logger[level](fullMessage, context)
    } else if (this.config.debug) {
      console[level](fullMessage, context)
    }
  }
}
