# Content模块重构技术方案设计

## 技术架构概述

### 架构模式

采用**中介者模式 + 模板方法模式 + 策略模式**的组合架构：

- **中介者模式**：BaseAIAdapter作为中介者，统一管理所有模块间的交互
- **模板方法模式**：BaseAIAdapter定义算法骨架，子类实现具体步骤
- **策略模式**：不同平台的适配器实现不同的策略
- **观察者模式**：事件总线实现组件间的松耦合通信

### 技术栈

- **语言**：TypeScript 5.8+
- **构建工具**：Vite 5.0+
- **测试框架**：Jest 29.6+
- **代码规范**：ESLint + Prettier
- **类型检查**：严格的TypeScript配置

## 核心模块设计

### 1. 中介者模块 (BaseAIAdapter)

```typescript
// 核心职责：作为中介者统一管理所有模块交互
abstract class BaseAIAdapter {
  // 依赖注入的核心组件
  protected eventBus: EventBus
  protected contentCapturer: ContentCapturer
  protected contentListener: ContentListener
  
  // 注入器管理
  protected injectors: Map<string, BaseInject>
  
  // 模板方法：定义初始化流程
  async initialize(): Promise<void>
  
  // 策略方法：子类实现
  abstract getSelectors(): SelectorConfig
  abstract extractConversation(): Promise<Conversation>
}
```

### 2. 事件总线模块 (EventBus)

```typescript
// 事件枚举定义
enum ContentEvent {
  INPUT_FOCUSED = 'input-focused',
  INPUT_CHANGED = 'input-changed',
  CONTENT_CAPTURED = 'content-captured',
  UI_INJECT_READY = 'ui-inject-ready'
}

// 事件总线实现
class EventBus {
  private listeners: Map<string, Set<EventCallback>>
  
  subscribe<T>(event: ContentEvent, callback: EventCallback<T>): void
  publish<T>(event: ContentEvent, data: T): void
  unsubscribe<T>(event: ContentEvent, callback: EventCallback<T>): void
}
```

### 3. 页面捕获模块 (Capturer)

```typescript
// 内容捕获器
class ContentCapturer {
  constructor(private eventBus: EventBus) {}
  
  // 捕获对话内容
  captureConversation(): Promise<Conversation>
  
  // 捕获页面元素
  captureElements(selectors: SelectorConfig): ElementMap
}

// 事件监听器
class ContentListener {
  constructor(private eventBus: EventBus) {}
  
  // 监听页面事件并发布到事件总线
  startListening(): void
  stopListening(): void
}
```

### 4. 注入模块 (Inject)

```typescript
// 基础注入器
abstract class BaseInject {
  constructor(
    protected adapter: BaseAIAdapter,
    protected eventBus: EventBus
  ) {}
  
  abstract inject(): Promise<void>
  abstract destroy(): void
}

// 具体注入器实现
class FloatingBubbleInject extends BaseInject {
  private component: FloatingBubble
  
  async inject(): Promise<void> {
    this.component = new FloatingBubble()
    // 注入逻辑
  }
}
```

### 5. UI组件模块 (Components)

```typescript
// 基础UI组件
abstract class BaseComponent {
  protected element: HTMLElement
  
  abstract render(): HTMLElement
  abstract destroy(): void
}

// 具体UI组件
class FloatingBubble extends BaseComponent {
  render(): HTMLElement {
    // 纯UI渲染逻辑
  }
  
  // 提供给inject调用的方法
  show(): void
  hide(): void
  updatePosition(x: number, y: number): void
}
```

## 数据流设计

### 初始化流程

```mermaid
sequenceDiagram
    participant Index as index.ts
    participant Detector as PlatformDetector
    participant Adapter as BaseAIAdapter
    participant EventBus as EventBus
    participant Capturer as ContentCapturer
    participant Listener as ContentListener
    participant Inject as Inject Classes
    
    Index->>Detector: detectPlatform()
    Detector-->>Index: platformConfig
    Index->>Adapter: new PlatformAdapter(config)
    Adapter->>EventBus: initialize()
    Adapter->>Capturer: initialize()
    Adapter->>Listener: startListening()
    Adapter->>Inject: injectAll()
    Inject-->>EventBus: subscribe events
```

### 事件流转

```mermaid
graph TD
    A[页面事件] --> B[ContentListener]
    B --> C[EventBus]
    C --> D[FloatingBubbleInject]
    C --> E[ArchiveButtonInject]
    C --> F[HistoryBubbleInject]
    D --> G[FloatingBubble Component]
    E --> H[ArchiveButton Component]
    F --> I[HistoryBubble Component]
```

## 接口设计

### 核心接口定义

```typescript
// 平台配置接口
interface PlatformConfig {
  name: string
  selectors: SelectorConfig
  patterns: PatternConfig
}

// 选择器配置
interface SelectorConfig {
  inputField: string[]
  sendButton: string[]
  messageContainer: string[]
  inputContainer: string[]
}

// 事件数据接口
interface EventData {
  [ContentEvent.INPUT_FOCUSED]: { element: HTMLElement; value: string }
  [ContentEvent.INPUT_CHANGED]: { element: HTMLElement; value: string }
  [ContentEvent.CONTENT_CAPTURED]: { conversation: Conversation }
}

// 注入器接口
interface IInject {
  inject(): Promise<void>
  destroy(): void
  isInjected(): boolean
}

// UI组件接口
interface IComponent {
  render(): HTMLElement
  destroy(): void
  isVisible(): boolean
}
```

## 文件组织结构

```
src/content/
├── index.ts                    # 入口文件 (~100行)
├── adapters/                   # 适配器模块
│   ├── BaseAIAdapter.ts       # 基础适配器 (~250行)
│   ├── chatgpt.ts            # ChatGPT适配器 (~150行)
│   ├── claude.ts             # Claude适配器 (~150行)
│   └── index.ts              # 适配器导出
├── shared/                    # 共享模块
│   ├── BackgroundHandler.ts  # Background通信 (~200行)
│   ├── EventBus.ts           # 事件总线 (~150行)
│   └── EventEnum.ts          # 事件枚举 (~50行)
├── capturer/                  # 捕获模块
│   ├── ContentCapturer.ts    # 内容捕获 (~200行)
│   ├── ContentListener.ts    # 事件监听 (~150行)
│   └── index.ts              # 捕获器导出
├── inject/                    # 注入模块
│   ├── BaseInject.ts         # 基础注入器 (~100行)
│   ├── FloatingBubbleInject.ts (~200行)
│   ├── ArchiveButtonInject.ts  (~200行)
│   ├── HistoryBubbleInject.ts  (~200行)
│   └── index.ts              # 注入器导出
├── components/                # UI组件
│   ├── BaseComponent.ts      # 基础组件 (~100行)
│   ├── FloatingBubble.ts     # 浮动气泡 (~250行)
│   ├── ArchiveButton.ts      # 存档按钮 (~200行)
│   ├── HistoryBubble.ts      # 历史气泡 (~250行)
│   └── index.ts              # 组件导出
├── types/                     # 类型定义
│   ├── events.ts             # 事件类型
│   ├── components.ts         # 组件类型
│   ├── adapters.ts           # 适配器类型
│   └── index.ts              # 类型导出
├── utils/                     # 工具类
│   ├── DOMUtils.ts           # DOM工具
│   ├── ElementFinder.ts      # 元素查找
│   └── index.ts              # 工具导出
└── configs/                   # 配置文件
    ├── selectors.ts          # 选择器配置
    ├── platforms.ts          # 平台配置
    └── index.ts              # 配置导出
```

## 性能优化策略

### 1. 懒加载机制
- UI组件按需加载
- 适配器延迟初始化
- 事件监听器动态注册

### 2. 内存管理
- 组件销毁时清理事件监听
- 定期清理无用的DOM引用
- 使用WeakMap避免内存泄漏

### 3. 事件优化
- 事件防抖处理
- 批量事件处理
- 事件委托机制

## 测试策略

### 1. 单元测试
- 每个模块独立测试
- Mock外部依赖
- 覆盖率要求80%+

### 2. 集成测试
- 模块间交互测试
- 事件流转测试
- 生命周期测试

### 3. E2E测试
- 真实浏览器环境测试
- 多平台兼容性测试
- 用户交互流程测试
