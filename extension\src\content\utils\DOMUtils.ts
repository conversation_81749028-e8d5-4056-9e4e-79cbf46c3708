/**
 * DOM工具类
 * 提供强大的DOM操作和元素查找功能
 */

import { IDOMUtils } from '../types/utils'

/**
 * DOM工具类实现
 */
export class DOMUtils implements IDOMUtils {
  private static instance: DOMUtils | null = null

  /**
   * 获取单例实例
   */
  static getInstance(): DOMUtils {
    if (!DOMUtils.instance) {
      DOMUtils.instance = new DOMUtils()
    }
    return DOMUtils.instance
  }

  /**
   * 等待页面加载完成
   */
  async waitForPageLoad(timeout: number = 10000): Promise<void> {
    if (document.readyState === 'complete') {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        document.removeEventListener('DOMContentLoaded', loadHandler)
        window.removeEventListener('load', loadHandler)
        reject(new Error('Page load timeout'))
      }, timeout)

      const loadHandler = () => {
        clearTimeout(timer)
        document.removeEventListener('DOMContentLoaded', loadHandler)
        window.removeEventListener('load', loadHandler)
        resolve()
      }

      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadHandler)
      } else {
        window.addEventListener('load', loadHandler)
      }
    })
  }

  /**
   * 等待元素出现
   */
  async waitForElement(selector: string, timeout: number = 5000): Promise<HTMLElement | null> {
    // 先检查元素是否已存在
    const existingElement = document.querySelector(selector) as HTMLElement
    if (existingElement) {
      return existingElement
    }

    return new Promise((resolve) => {
      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector) as HTMLElement
        if (element) {
          obs.disconnect()
          resolve(element)
        }
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true
      })

      // 超时处理
      setTimeout(() => {
        observer.disconnect()
        resolve(null)
      }, timeout)
    })
  }

  /**
   * 等待元素消失
   */
  async waitForElementToDisappear(selector: string, timeout: number = 5000): Promise<void> {
    const element = document.querySelector(selector)
    if (!element) {
      return Promise.resolve()
    }

    return new Promise((resolve) => {
      const observer = new MutationObserver((mutations, obs) => {
        const stillExists = document.querySelector(selector)
        if (!stillExists) {
          obs.disconnect()
          resolve()
        }
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true
      })

      // 超时处理
      setTimeout(() => {
        observer.disconnect()
        resolve()
      }, timeout)
    })
  }

  /**
   * 检查元素是否可见
   */
  isElementVisible(element: HTMLElement): boolean {
    if (!element || !element.offsetParent) {
      return false
    }

    const rect = element.getBoundingClientRect()
    const style = window.getComputedStyle(element)

    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0' &&
      rect.top < window.innerHeight &&
      rect.bottom > 0 &&
      rect.left < window.innerWidth &&
      rect.right > 0
    )
  }

  /**
   * 获取元素的绝对位置
   */
  getElementPosition(element: HTMLElement): DOMRect {
    return element.getBoundingClientRect()
  }

  /**
   * 检查元素是否在视口内
   */
  isElementInViewport(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect()
    
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    )
  }

  /**
   * 滚动到元素位置
   */
  scrollToElement(element: HTMLElement, behavior: ScrollBehavior = 'smooth'): void {
    element.scrollIntoView({
      behavior,
      block: 'center',
      inline: 'center'
    })
  }

  /**
   * 创建元素
   */
  createElement<K extends keyof HTMLElementTagNameMap>(
    tagName: K,
    attributes?: Record<string, string>,
    styles?: Record<string, string>
  ): HTMLElementTagNameMap[K] {
    const element = document.createElement(tagName)

    // 设置属性
    if (attributes) {
      Object.entries(attributes).forEach(([key, value]) => {
        element.setAttribute(key, value)
      })
    }

    // 设置样式
    if (styles) {
      Object.entries(styles).forEach(([key, value]) => {
        ;(element.style as any)[key] = value
      })
    }

    return element
  }

  /**
   * 安全地移除元素
   */
  safeRemoveElement(element: HTMLElement): void {
    try {
      if (element && element.parentNode) {
        element.parentNode.removeChild(element)
      }
    } catch (error) {
      console.warn('Failed to remove element:', error)
    }
  }

  /**
   * 克隆元素
   */
  cloneElement(element: HTMLElement, deep: boolean = true): HTMLElement {
    return element.cloneNode(deep) as HTMLElement
  }

  /**
   * 查找最近的匹配祖先元素
   */
  findClosestAncestor(element: HTMLElement, selector: string): HTMLElement | null {
    return element.closest(selector) as HTMLElement | null
  }

  /**
   * 查找所有匹配的子元素
   */
  findAllChildren(element: HTMLElement, selector: string): HTMLElement[] {
    return Array.from(element.querySelectorAll(selector)) as HTMLElement[]
  }

  /**
   * 检查元素是否匹配选择器
   */
  matchesSelector(element: HTMLElement, selector: string): boolean {
    try {
      return element.matches(selector)
    } catch (error) {
      console.warn(`Invalid selector: ${selector}`, error)
      return false
    }
  }

  /**
   * 获取元素的文本内容（清理后）
   */
  getCleanTextContent(element: HTMLElement): string {
    return (element.textContent || '').replace(/\s+/g, ' ').trim()
  }

  /**
   * 获取元素的HTML内容
   */
  getHTMLContent(element: HTMLElement): string {
    return element.innerHTML
  }

  /**
   * 设置元素的文本内容
   */
  setTextContent(element: HTMLElement, text: string): void {
    element.textContent = text
  }

  /**
   * 设置元素的HTML内容
   */
  setHTMLContent(element: HTMLElement, html: string): void {
    element.innerHTML = html
  }

  /**
   * 添加CSS类
   */
  addClass(element: HTMLElement, className: string): void {
    element.classList.add(className)
  }

  /**
   * 移除CSS类
   */
  removeClass(element: HTMLElement, className: string): void {
    element.classList.remove(className)
  }

  /**
   * 切换CSS类
   */
  toggleClass(element: HTMLElement, className: string): void {
    element.classList.toggle(className)
  }

  /**
   * 检查是否有CSS类
   */
  hasClass(element: HTMLElement, className: string): boolean {
    return element.classList.contains(className)
  }

  /**
   * 设置CSS样式
   */
  setStyle(element: HTMLElement, property: string, value: string): void {
    ;(element.style as any)[property] = value
  }

  /**
   * 获取CSS样式
   */
  getStyle(element: HTMLElement, property: string): string {
    return window.getComputedStyle(element).getPropertyValue(property)
  }

  /**
   * 设置多个CSS样式
   */
  setStyles(element: HTMLElement, styles: Record<string, string>): void {
    Object.entries(styles).forEach(([property, value]) => {
      ;(element.style as any)[property] = value
    })
  }

  /**
   * 获取元素的数据属性
   */
  getDataAttribute(element: HTMLElement, name: string): string | null {
    return element.getAttribute(`data-${name}`)
  }

  /**
   * 设置元素的数据属性
   */
  setDataAttribute(element: HTMLElement, name: string, value: string): void {
    element.setAttribute(`data-${name}`, value)
  }

  /**
   * 检查元素是否为输入元素
   */
  isInputElement(element: HTMLElement): boolean {
    const inputTags = ['INPUT', 'TEXTAREA', 'SELECT']
    return inputTags.includes(element.tagName) || element.contentEditable === 'true'
  }

  /**
   * 获取输入元素的值
   */
  getInputValue(element: HTMLElement): string {
    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      return (element as HTMLInputElement).value
    }
    
    if (element.contentEditable === 'true') {
      return element.textContent || ''
    }
    
    return ''
  }

  /**
   * 设置输入元素的值
   */
  setInputValue(element: HTMLElement, value: string): void {
    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      ;(element as HTMLInputElement).value = value
      
      // 触发input事件
      element.dispatchEvent(new Event('input', { bubbles: true }))
    } else if (element.contentEditable === 'true') {
      element.textContent = value
      
      // 触发input事件
      element.dispatchEvent(new Event('input', { bubbles: true }))
    }
  }

  /**
   * 模拟点击事件
   */
  simulateClick(element: HTMLElement): void {
    const event = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    })
    element.dispatchEvent(event)
  }

  /**
   * 模拟键盘事件
   */
  simulateKeyPress(element: HTMLElement, key: string): void {
    const keydownEvent = new KeyboardEvent('keydown', {
      key,
      bubbles: true,
      cancelable: true
    })
    
    const keyupEvent = new KeyboardEvent('keyup', {
      key,
      bubbles: true,
      cancelable: true
    })

    element.dispatchEvent(keydownEvent)
    element.dispatchEvent(keyupEvent)
  }

  /**
   * 获取元素的唯一标识符
   */
  getElementIdentifier(element: HTMLElement): string {
    // 优先使用ID
    if (element.id) {
      return `#${element.id}`
    }

    // 使用类名
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.trim())
      if (classes.length > 0) {
        return `.${classes.join('.')}`
      }
    }

    // 使用标签名和位置
    const tagName = element.tagName.toLowerCase()
    const parent = element.parentElement
    if (parent) {
      const siblings = Array.from(parent.children).filter(child => 
        child.tagName.toLowerCase() === tagName
      )
      const index = siblings.indexOf(element)
      return `${tagName}:nth-of-type(${index + 1})`
    }

    return tagName
  }
}
