/**
 * 基础注入器抽象类
 * 定义所有注入器的基本接口和生命周期管理
 */

import { 
  IInject, 
  InjectConfig, 
  ComponentLifecycleState 
} from '../types/components'
import { IEventBus, EventCallback, ContentEventType } from '../types/events'
import { IAIAdapter } from '../types/adapters'
import { ILogger } from '../types/utils'

/**
 * 基础注入器抽象类
 * 提供注入器的基本功能和生命周期管理
 */
export abstract class BaseInject implements IInject {
  /** 注入器ID */
  public readonly id: string
  /** 注入器名称 */
  public readonly name: string
  /** 注入器配置 */
  protected readonly config: InjectConfig
  /** 生命周期状态 */
  protected _state: ComponentLifecycleState = ComponentLifecycleState.UNINITIALIZED
  /** 是否已注入 */
  protected _isInjected: boolean = false
  /** 适配器实例 */
  protected adapter: IAIAdapter
  /** 事件总线 */
  protected eventBus: IEventBus
  /** 日志器 */
  protected logger?: ILogger
  /** 事件监听器清理函数 */
  protected eventCleanupFunctions: (() => void)[] = []
  /** DOM监听器清理函数 */
  protected domCleanupFunctions: (() => void)[] = []
  /** 定时器ID列表 */
  protected timerIds: number[] = []
  /** 重试计数 */
  protected retryCount: number = 0

  constructor(
    adapter: IAIAdapter,
    eventBus: IEventBus,
    config: InjectConfig = {}
  ) {
    this.adapter = adapter
    this.eventBus = eventBus
    this.id = config.id || this.generateId()
    this.name = config.name || this.constructor.name
    this.config = {
      enabled: true,
      debug: false,
      injectDelay: 0,
      retryCount: 3,
      retryInterval: 1000,
      ...config
    }
    
    if (this.config.debug) {
      console.log(`[${this.name}] Inject created with ID: ${this.id}`)
    }
  }

  /**
   * 获取生命周期状态
   */
  get state(): ComponentLifecycleState {
    return this._state
  }

  /**
   * 获取是否已注入
   */
  get isInjected(): boolean {
    return this._isInjected
  }

  /**
   * 执行注入（抽象方法，子类必须实现）
   */
  abstract inject(): Promise<void>

  /**
   * 取消注入
   */
  async uninject(): Promise<void> {
    if (!this._isInjected || this._state === ComponentLifecycleState.DESTROYED) {
      return
    }

    this._state = ComponentLifecycleState.DESTROYING

    try {
      // 清理所有资源
      this.cleanup()
      
      // 调用子类的取消注入方法
      await this.onUninject()
      
      this._isInjected = false
      this._state = ComponentLifecycleState.UNINITIALIZED
      
      if (this.config.debug) {
        console.log(`[${this.name}] Inject removed`)
      }
    } catch (error) {
      console.error(`[${this.name}] Error during uninject:`, error)
      this._state = ComponentLifecycleState.ERROR
    }
  }

  /**
   * 销毁注入器
   */
  destroy(): void {
    if (this._state === ComponentLifecycleState.DESTROYED) {
      return
    }

    this._state = ComponentLifecycleState.DESTROYING

    try {
      // 清理所有资源
      this.cleanup()
      
      // 调用子类的销毁方法
      this.onDestroy()
      
      this._isInjected = false
      this._state = ComponentLifecycleState.DESTROYED
      
      if (this.config.debug) {
        console.log(`[${this.name}] Inject destroyed`)
      }
    } catch (error) {
      console.error(`[${this.name}] Error during destruction:`, error)
      this._state = ComponentLifecycleState.DESTROYED
    }
  }

  /**
   * 获取注入状态
   */
  getState(): ComponentLifecycleState {
    return this._state
  }

  /**
   * 设置日志器
   */
  setLogger(logger: ILogger): void {
    this.logger = logger
  }

  /**
   * 执行带重试的注入
   */
  protected async executeWithRetry(): Promise<void> {
    if (!this.config.enabled) {
      this.log('info', 'Inject is disabled, skipping')
      return
    }

    this._state = ComponentLifecycleState.INITIALIZING
    this.retryCount = 0

    while (this.retryCount <= (this.config.retryCount || 3)) {
      try {
        // 应用注入延迟
        if (this.config.injectDelay && this.config.injectDelay > 0) {
          await this.delay(this.config.injectDelay)
        }

        // 执行注入
        await this.inject()
        
        this._isInjected = true
        this._state = ComponentLifecycleState.RUNNING
        
        this.log('info', 'Inject completed successfully')
        return
        
      } catch (error) {
        this.retryCount++
        this.log('error', `Inject failed (attempt ${this.retryCount}):`, error)
        
        if (this.retryCount > (this.config.retryCount || 3)) {
          this._state = ComponentLifecycleState.ERROR
          throw error
        }
        
        // 等待重试间隔
        if (this.config.retryInterval && this.config.retryInterval > 0) {
          await this.delay(this.config.retryInterval)
        }
      }
    }
  }

  /**
   * 添加事件监听器（自动管理清理）
   */
  protected addEventListner(
    event: ContentEventType,
    callback: EventCallback,
    config?: any
  ): void {
    const unsubscribe = this.eventBus.subscribe(event, callback, config)
    this.eventCleanupFunctions.push(unsubscribe)
  }

  /**
   * 添加DOM事件监听器（自动管理清理）
   */
  protected addDOMEventListener<K extends keyof HTMLElementEventMap>(
    element: HTMLElement,
    type: K,
    listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any,
    options?: boolean | AddEventListenerOptions
  ): void {
    element.addEventListener(type, listener, options)
    
    this.domCleanupFunctions.push(() => {
      element.removeEventListener(type, listener, options)
    })
  }

  /**
   * 添加全局DOM事件监听器（自动管理清理）
   */
  protected addGlobalEventListener<K extends keyof WindowEventMap>(
    type: K,
    listener: (this: Window, ev: WindowEventMap[K]) => any,
    options?: boolean | AddEventListenerOptions
  ): void {
    window.addEventListener(type, listener, options)
    
    this.domCleanupFunctions.push(() => {
      window.removeEventListener(type, listener, options)
    })
  }

  /**
   * 设置定时器（自动管理清理）
   */
  protected setTimeout(callback: () => void, delay: number): number {
    const timerId = window.setTimeout(callback, delay)
    this.timerIds.push(timerId)
    return timerId
  }

  /**
   * 设置间隔定时器（自动管理清理）
   */
  protected setInterval(callback: () => void, interval: number): number {
    const timerId = window.setInterval(callback, interval)
    this.timerIds.push(timerId)
    return timerId
  }

  /**
   * 等待指定时间
   */
  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => {
      this.setTimeout(() => resolve(), ms)
    })
  }

  /**
   * 等待元素出现
   */
  protected waitForElement(
    selector: string,
    timeout: number = 5000
  ): Promise<HTMLElement | null> {
    return new Promise((resolve) => {
      const element = document.querySelector(selector) as HTMLElement
      if (element) {
        resolve(element)
        return
      }

      const observer = new MutationObserver(() => {
        const element = document.querySelector(selector) as HTMLElement
        if (element) {
          observer.disconnect()
          resolve(element)
        }
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true
      })

      // 超时处理
      this.setTimeout(() => {
        observer.disconnect()
        resolve(null)
      }, timeout)
    })
  }

  /**
   * 检查元素是否存在
   */
  protected elementExists(selector: string): boolean {
    return document.querySelector(selector) !== null
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `inject_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 清理所有资源
   */
  private cleanup(): void {
    // 清理事件监听器
    this.eventCleanupFunctions.forEach(cleanup => {
      try {
        cleanup()
      } catch (error) {
        console.error(`[${this.name}] Error during event cleanup:`, error)
      }
    })
    this.eventCleanupFunctions = []

    // 清理DOM事件监听器
    this.domCleanupFunctions.forEach(cleanup => {
      try {
        cleanup()
      } catch (error) {
        console.error(`[${this.name}] Error during DOM cleanup:`, error)
      }
    })
    this.domCleanupFunctions = []

    // 清理定时器
    this.timerIds.forEach(timerId => {
      try {
        clearTimeout(timerId)
        clearInterval(timerId)
      } catch (error) {
        console.error(`[${this.name}] Error during timer cleanup:`, error)
      }
    })
    this.timerIds = []
  }

  // 生命周期钩子方法（子类可重写）

  /**
   * 取消注入时调用
   */
  protected async onUninject(): Promise<void> {
    // 子类可重写
  }

  /**
   * 销毁时调用
   */
  protected onDestroy(): void {
    // 子类可重写
  }

  /**
   * 记录日志
   */
  protected log(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: any): void {
    const fullMessage = `[${this.name}] ${message}`
    
    if (this.logger) {
      this.logger[level](fullMessage, context)
    } else if (this.config.debug) {
      console[level](fullMessage, context)
    }
  }
}
