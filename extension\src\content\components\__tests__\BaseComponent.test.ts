/**
 * BaseComponent 单元测试
 */

import { BaseComponent } from '../BaseComponent'
import { ComponentLifecycleState } from '../../types/components'

// 创建测试用的具体组件类
class TestComponent extends BaseComponent {
  private renderCount = 0

  render(): HTMLElement {
    this.renderCount++
    const element = this.createElement('div', {
      'data-testid': 'test-component',
      'id': this.id
    }, {
      width: '100px',
      height: '100px',
      backgroundColor: 'red'
    }, 'Test Component')

    this.applyCustomStyles(element)
    this.applyCustomAttributes(element)
    
    return element
  }

  getRenderCount(): number {
    return this.renderCount
  }

  // 暴露受保护的方法用于测试
  public testCreateElement = this.createElement.bind(this)
  public testIsElementVisible = this.isElementVisible.bind(this)
  public testLog = this.log.bind(this)
}

describe('BaseComponent', () => {
  let component: TestComponent

  beforeEach(() => {
    // 清理DOM
    document.body.innerHTML = ''
    
    component = new TestComponent({
      name: 'TestComponent',
      debug: false
    })
  })

  afterEach(() => {
    if (component) {
      component.destroy()
    }
  })

  describe('基本属性', () => {
    test('应该正确初始化基本属性', () => {
      expect(component.id).toBeDefined()
      expect(component.name).toBe('TestComponent')
      expect(component.state).toBe(ComponentLifecycleState.UNINITIALIZED)
      expect(component.isVisible).toBe(false)
      expect(component.element).toBeNull()
    })

    test('应该生成唯一的ID', () => {
      const component2 = new TestComponent()
      expect(component.id).not.toBe(component2.id)
      component2.destroy()
    })

    test('应该使用配置中的ID', () => {
      const customComponent = new TestComponent({ id: 'custom-id' })
      expect(customComponent.id).toBe('custom-id')
      customComponent.destroy()
    })
  })

  describe('生命周期管理', () => {
    test('应该正确处理显示流程', () => {
      expect(component.state).toBe(ComponentLifecycleState.UNINITIALIZED)
      expect(component.element).toBeNull()
      
      component.show()
      
      expect(component.state).toBe(ComponentLifecycleState.INITIALIZED)
      expect(component.element).not.toBeNull()
      expect(component.element?.tagName).toBe('DIV')
      expect(component.element?.textContent).toBe('Test Component')
    })

    test('应该正确处理隐藏流程', () => {
      component.show()
      expect(component.element?.style.display).not.toBe('none')
      
      component.hide()
      expect(component.element?.style.display).toBe('none')
    })

    test('应该正确处理销毁流程', () => {
      component.show()
      document.body.appendChild(component.element!)
      
      expect(component.state).toBe(ComponentLifecycleState.INITIALIZED)
      expect(document.body.contains(component.element)).toBe(true)
      
      component.destroy()
      
      expect(component.state).toBe(ComponentLifecycleState.DESTROYED)
      expect(component.element).toBeNull()
      expect(document.body.children.length).toBe(0)
    })

    test('应该防止重复销毁', () => {
      component.show()
      component.destroy()
      
      expect(component.state).toBe(ComponentLifecycleState.DESTROYED)
      
      // 再次销毁不应该抛出错误
      expect(() => component.destroy()).not.toThrow()
      expect(component.state).toBe(ComponentLifecycleState.DESTROYED)
    })

    test('应该防止对已销毁组件的操作', () => {
      component.destroy()
      
      expect(() => component.show()).toThrow()
      expect(() => component.update()).toThrow()
    })
  })

  describe('DOM操作', () => {
    test('应该正确创建DOM元素', () => {
      const element = component.testCreateElement('span', {
        'class': 'test-class',
        'data-value': 'test'
      }, {
        color: 'blue',
        fontSize: '14px'
      }, 'Test Text')

      expect(element.tagName).toBe('SPAN')
      expect(element.className).toBe('test-class')
      expect(element.getAttribute('data-value')).toBe('test')
      expect(element.style.color).toBe('blue')
      expect(element.style.fontSize).toBe('14px')
      expect(element.textContent).toBe('Test Text')
    })

    test('应该正确检测元素可见性', () => {
      const visibleElement = document.createElement('div')
      visibleElement.style.width = '100px'
      visibleElement.style.height = '100px'
      document.body.appendChild(visibleElement)

      const hiddenElement = document.createElement('div')
      hiddenElement.style.display = 'none'
      document.body.appendChild(hiddenElement)

      expect(component.testIsElementVisible(visibleElement)).toBe(true)
      expect(component.testIsElementVisible(hiddenElement)).toBe(false)
    })

    test('应该应用自定义样式和属性', () => {
      const customComponent = new TestComponent({
        customStyles: {
          border: '1px solid black',
          padding: '10px'
        },
        customAttributes: {
          'data-custom': 'value',
          'role': 'button'
        }
      })

      customComponent.show()
      const element = customComponent.element!

      expect(element.style.border).toBe('1px solid black')
      expect(element.style.padding).toBe('10px')
      expect(element.getAttribute('data-custom')).toBe('value')
      expect(element.getAttribute('role')).toBe('button')

      customComponent.destroy()
    })
  })

  describe('事件管理', () => {
    test('应该正确管理事件监听器', () => {
      const mockCallback = jest.fn()
      component.show()
      const element = component.element!

      // 使用反射访问受保护的方法
      ;(component as any).addEventListener(element, 'click', mockCallback)

      // 触发事件
      element.click()
      expect(mockCallback).toHaveBeenCalledTimes(1)

      // 销毁组件应该清理事件监听器
      component.destroy()
      element.click()
      expect(mockCallback).toHaveBeenCalledTimes(1) // 不应该再次调用
    })

    test('应该正确管理全局事件监听器', () => {
      const mockCallback = jest.fn()
      component.show()

      // 使用反射访问受保护的方法
      ;(component as any).addGlobalEventListener('resize', mockCallback)

      // 触发事件
      window.dispatchEvent(new Event('resize'))
      expect(mockCallback).toHaveBeenCalledTimes(1)

      // 销毁组件应该清理事件监听器
      component.destroy()
      window.dispatchEvent(new Event('resize'))
      expect(mockCallback).toHaveBeenCalledTimes(1) // 不应该再次调用
    })
  })

  describe('更新机制', () => {
    test('应该正确处理更新', () => {
      const onUpdateSpy = jest.spyOn(component as any, 'onUpdate')
      component.show()

      component.update({ test: 'data' })

      expect(onUpdateSpy).toHaveBeenCalledWith({ test: 'data' })
    })

    test('应该处理更新时的错误', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      // 模拟onUpdate抛出错误
      jest.spyOn(component as any, 'onUpdate').mockImplementation(() => {
        throw new Error('Update error')
      })

      component.show()
      
      expect(() => component.update()).not.toThrow()
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error during component update'),
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })
  })

  describe('日志功能', () => {
    test('应该在调试模式下记录日志', () => {
      const consoleSpy = jest.spyOn(console, 'info').mockImplementation()
      const debugComponent = new TestComponent({ debug: true })

      debugComponent.testLog('info', 'Test message', { data: 'test' })

      expect(consoleSpy).toHaveBeenCalledWith(
        '[TestComponent] Test message',
        { data: 'test' }
      )

      consoleSpy.mockRestore()
      debugComponent.destroy()
    })

    test('应该使用外部日志器', () => {
      const mockLogger = {
        debug: jest.fn(),
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn()
      }

      component.setLogger(mockLogger as any)
      component.testLog('info', 'Test message')

      expect(mockLogger.info).toHaveBeenCalledWith(
        '[TestComponent] Test message',
        undefined
      )
    })
  })

  describe('渲染优化', () => {
    test('应该只在需要时渲染', () => {
      expect(component.getRenderCount()).toBe(0)
      
      component.show()
      expect(component.getRenderCount()).toBe(1)
      
      // 再次显示不应该重新渲染
      component.show()
      expect(component.getRenderCount()).toBe(1)
      
      component.hide()
      component.show()
      expect(component.getRenderCount()).toBe(1) // 仍然是1，因为元素已存在
    })
  })
})
