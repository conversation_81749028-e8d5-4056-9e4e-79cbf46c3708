import { dexieDatabase, ChatHistoryWithPlatform, ChatHistoryWithPlatformAndPrompt } from '../database/dexie'
import { ChatHistory, ChatPrompt } from '../../types/database_entity'
import {
  CreateChatHistoryInput,
  UpdateChatHistoryInput,
  ChatHistoryQueryParams,
  PaginatedResult,
  SearchResult,
  DatabaseResult,
  CreateChatWithPromptInput,
  CreateChatWithPromptResult,
  ChatPromptDetailResult
} from '../../types/database_dto'
import { chatPromptService } from './chatPromptDexie'

export class ChatHistoryService {
  private static instance: ChatHistoryService

  public static getInstance(): ChatHistoryService {
    if (!ChatHistoryService.instance) {
      ChatHistoryService.instance = new ChatHistoryService()
    }
    return ChatHistoryService.instance
  }

  /**
   * 创建聊天记录（包含提示词和历史记录）
   */
  async createWithPrompt(input: CreateChatWithPromptInput): Promise<DatabaseResult<CreateChatWithPromptResult>> {
    try {
      console.log('【EchoSync】Starting create chat with prompt:', input)
      await dexieDatabase.initialize()

      const now = Date.now()
      let chatPrompt: ChatPrompt
      let chatUid: string

      // 检查是否存在相同提示词
      const existingPrompt = await chatPromptService.findByPrompt(input.chat_prompt)

      if (existingPrompt.success && existingPrompt.data) {
        // 复用现有提示词的chat_uid
        chatPrompt = existingPrompt.data
        chatUid = chatPrompt.chat_uid
        console.log('【EchoSync】Reusing existing prompt with chat_uid:', chatUid)
      } else {
        // 创建新的提示词记录
        chatUid = now.toString()
        const promptResult = await chatPromptService.create({
          chat_prompt: input.chat_prompt,
          chat_uid: chatUid,
          create_time: input.create_time || now
        })

        if (!promptResult.success || !promptResult.data) {
          return {
            success: false,
            error: promptResult.error || 'Failed to create chat prompt'
          }
        }

        chatPrompt = promptResult.data
        console.log('【EchoSync】Created new prompt with chat_uid:', chatUid)
      }

      // 创建历史记录
      const historyData: Omit<ChatHistory, 'id'> = {
        chat_answer: input.chat_answer || null,
        chat_uid: chatUid,
        platform_id: input.platform_id,
        tags: typeof input.tags === 'string' ? input.tags : JSON.stringify(input.tags || []),
        chat_group_name: input.chat_group_name || null,
        chat_sort: input.chat_sort || 0,
        p_uid: input.p_uid || null,
        is_synced: 0,
        is_answered: input.chat_answer ? 1 : 0,
        is_delete: 0,
        create_time: input.create_time || now
      }

      const historyId = await dexieDatabase.chatHistory.add(historyData)
      const chatHistory = { ...historyData, id: historyId as number }

      console.log('【EchoSync】Chat history created with id:', historyId)

      return {
        success: true,
        data: {
          chatPrompt,
          chatHistory
        }
      }
    } catch (error) {
      console.error('Create chat with prompt error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 创建聊天历史记录（保持向后兼容）
   */
  async create(input: CreateChatHistoryInput): Promise<DatabaseResult<ChatHistory>> {
    try {
      console.log('【EchoSync】Starting create chat history with input:', input)

      // 如果包含chat_prompt，使用新的双表操作方法
      if (input.chat_prompt) {
        const result = await this.createWithPrompt({
          chat_prompt: input.chat_prompt,
          chat_answer: input.chat_answer,
          platform_id: input.platform_id,
          tags: input.tags,
          chat_group_name: input.chat_group_name,
          chat_sort: input.chat_sort,
          p_uid: input.p_uid,
          create_time: input.create_time
        })

        if (!result.success || !result.data) {
          return {
            success: false,
            error: result.error
          }
        }

        return {
          success: true,
          data: result.data.chatHistory
        }
      }

      // 如果没有chat_prompt，直接创建历史记录
      await dexieDatabase.initialize()
      console.log('【EchoSync】Database initialized successfully')

      const now = Date.now()
      const data: Omit<ChatHistory, 'id'> = {
        chat_answer: input.chat_answer || null,
        chat_uid: input.chat_uid || Date.now().toString(),
        platform_id: input.platform_id,
        tags: typeof input.tags === 'string' ? input.tags : JSON.stringify(input.tags || []),
        chat_group_name: input.chat_group_name || null,
        chat_sort: input.chat_sort || 0,
        p_uid: input.p_uid || null,
        is_synced: 0,
        is_answered: input.chat_answer ? 1 : 0,
        is_delete: 0,
        create_time: input.create_time || now
      }

      console.log('【EchoSync】Prepared data for insertion:', data)
      const id = await dexieDatabase.chatHistory.add(data as ChatHistory)
      console.log('【EchoSync】Data inserted with ID:', id)

      const record = await dexieDatabase.chatHistory.get(id)
      console.log('【EchoSync】Retrieved record:', record)

      return {
        success: true,
        data: record!
      }
    } catch (error) {
      console.error('【EchoSync】Create chat history error:', error)
      console.error('【EchoSync】Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      })
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据ID获取聊天历史记录
   */
  async getById(id: number): Promise<DatabaseResult<ChatHistory>> {
    try {
      await dexieDatabase.initialize()
      
      const record = await dexieDatabase.chatHistory
        .where('id')
        .equals(id)
        .and(item => item.is_delete === 0)
        .first()

      if (!record) {
        return {
          success: false,
          error: 'Record not found'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Get chat history by id error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 更新聊天历史记录
   */
  async update(id: number, input: UpdateChatHistoryInput): Promise<DatabaseResult<ChatHistory>> {
    try {
      await dexieDatabase.initialize()
      
      const updateData: Partial<ChatHistory> = {
        ...input,
        tags: input.tags ? JSON.stringify(input.tags) : undefined
      }

      await dexieDatabase.chatHistory.update(id, updateData)
      const record = await dexieDatabase.chatHistory.get(id)

      if (!record) {
        return {
          success: false,
          error: 'Record not found after update'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Update chat history error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 软删除聊天历史记录
   */
  async delete(id: number): Promise<DatabaseResult<boolean>> {
    try {
      await dexieDatabase.initialize()
      
      await dexieDatabase.chatHistory.update(id, { is_delete: 1 })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Delete chat history error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 批量删除聊天历史记录
   */
  async batchDelete(ids: number[]): Promise<DatabaseResult<boolean>> {
    try {
      await dexieDatabase.initialize()
      
      await dexieDatabase.chatHistory
        .where('id')
        .anyOf(ids)
        .modify({ is_delete: 1 })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Batch delete chat history error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取聊天历史列表（带分页）
   */
  async getList(params: ChatHistoryQueryParams = {}): Promise<DatabaseResult<PaginatedResult<ChatHistoryWithPlatform>>> {
    try {
      await dexieDatabase.initialize()
      
      const {
        page = 1,
        limit = 20,
        platform_id,
        order_by = 'create_time',
        order_direction = 'DESC'
      } = params

      const offset = (page - 1) * limit

      const result = await dexieDatabase.getChatHistoryWithPlatform({
        limit,
        offset,
        platform_id,
        order_by,
        order_direction
      })

      // 获取总数
      let totalQuery = dexieDatabase.chatHistory.where('is_delete').equals(0)
      if (platform_id) {
        totalQuery = totalQuery.and(item => item.platform_id === platform_id)
      }
      const total = await totalQuery.count()

      return {
        success: true,
        data: {
          data: result,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      console.error('Get chat history list error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 搜索聊天历史
   */
  async search(searchTerm: string, params: ChatHistoryQueryParams = {}): Promise<DatabaseResult<SearchResult<ChatHistoryWithPlatform>>> {
    try {
      await dexieDatabase.initialize()
      
      const {
        page = 1,
        limit = 20,
        platform_id
      } = params

      const offset = (page - 1) * limit

      const results = await dexieDatabase.searchChatHistory(searchTerm, {
        limit: limit + offset, // 获取更多数据用于分页
        platform_id
      })

      // 手动分页
      const paginatedResults = results.slice(offset, offset + limit)

      return {
        success: true,
        data: {
          data: paginatedResults,
          total: results.length,
          page,
          limit,
          totalPages: Math.ceil(results.length / limit),
          searchTerm
        }
      }
    } catch (error) {
      console.error('Search chat history error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取去重的聊天历史
   */
  async getUniqueChats(params: { limit?: number; order_direction?: 'ASC' | 'DESC' } = {}): Promise<DatabaseResult<ChatHistoryWithPlatformAndPrompt[]>> {
    try {
      await dexieDatabase.initialize()
      
      const result = await dexieDatabase.getUniqueChats(params)

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('Get unique chats error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据提示词内容查找聊天提示词（已废弃，使用chatPromptService）
   * @deprecated Use chatPromptService.findByPrompt instead
   */
  async findByChatPrompt(prompt: string): Promise<DatabaseResult<ChatHistory | null>> {
    console.warn('findByChatPrompt is deprecated, use chatPromptService.findByPrompt instead')
    return {
      success: true,
      data: null
    }
  }

  /**
   * 根据提示词内容查找已存在的chat_uid（已废弃，使用chatPromptService）
   * @deprecated Use chatPromptService.findByPrompt instead
   */
  async findExistingChatUid(prompt: string): Promise<string | null> {
    console.warn('findExistingChatUid is deprecated, use chatPromptService.findByPrompt instead')
    const result = await chatPromptService.findByPrompt(prompt)
    return result.success && result.data ? result.data.chat_uid : null
  }

  /**
   * 根据 chat_uid 获取聊天历史
   */
  async getByChatUid(chatUid: string): Promise<DatabaseResult<ChatHistory[]>> {
    try {
      await dexieDatabase.initialize()

      const records = await dexieDatabase.chatHistory
        .where('chat_uid')
        .equals(chatUid)
        .and(item => item.is_delete === 0)
        .toArray()

      return {
        success: true,
        data: records
      }
    } catch (error) {
      console.error('Get chat history by chat_uid error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取完整的聊天信息（提示词+历史记录）
   */
  async getFullChatInfo(chatUid: string): Promise<DatabaseResult<ChatPromptDetailResult>> {
    try {
      await dexieDatabase.initialize()

      const result = await dexieDatabase.getChatPromptDetail(chatUid)

      if (!result.prompt) {
        return {
          success: false,
          error: 'Chat prompt not found'
        }
      }

      return {
        success: true,
        data: {
          prompt: result.prompt,
          histories: result.histories
        }
      }
    } catch (error) {
      console.error('Get full chat info error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 批量创建答案记录（性能优化）
   */
  async createAnswerBatch(answers: Array<{
    chat_answer: string
    chat_uid: string
    platform_id: number
    create_time?: number
  }>): Promise<DatabaseResult<ChatHistory[]>> {
    try {
      console.log('【EchoSync】Starting batch create answers:', answers.length, 'items')
      await dexieDatabase.initialize()

      const now = Date.now()
      const historyData: Array<Omit<ChatHistory, 'id'>> = answers.map(answer => ({
        chat_answer: answer.chat_answer,
        chat_uid: answer.chat_uid,
        platform_id: answer.platform_id,
        tags: null,
        chat_group_name: null,
        chat_sort: 0,
        p_uid: null,
        is_synced: 0,
        is_answered: 1, // 这些都是答案记录
        is_delete: 0,
        create_time: answer.create_time || now
      }))

      // 使用 Dexie 的批量插入
      const ids = await dexieDatabase.chatHistory.bulkAdd(historyData as any, { allKeys: true })

      const results: ChatHistory[] = historyData.map((data, index) => ({
        ...data,
        id: ids[index] as number
      }))

      console.log('【EchoSync】Batch created', results.length, 'answer records')

      return {
        success: true,
        data: results
      }
    } catch (error) {
      console.error('【EchoSync】Batch create answers error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取提示词列表（带平台统计）
   */
  async getPromptsWithPlatforms(params: { limit?: number; offset?: number; order_direction?: 'ASC' | 'DESC' } = {}): Promise<DatabaseResult<any[]>> {
    try {
      await dexieDatabase.initialize()

      const result = await dexieDatabase.getPromptsWithPlatforms(params)

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('Get prompts with platforms error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 更新聊天记录的答案
   */
  async updateAnswer(chatUid: string, platformId: number, answer: string): Promise<DatabaseResult<ChatHistory>> {
    try {
      await dexieDatabase.initialize()

      // 查找对应的历史记录
      const history = await dexieDatabase.chatHistory
        .where('chat_uid')
        .equals(chatUid)
        .and(item => item.platform_id === platformId && item.is_delete === 0)
        .first()

      if (!history) {
        return {
          success: false,
          error: 'Chat history not found'
        }
      }

      // 更新答案和状态
      await dexieDatabase.chatHistory.update(history.id!, {
        chat_answer: answer,
        is_answered: 1
      })

      const updatedHistory = await dexieDatabase.chatHistory.get(history.id!)

      return {
        success: true,
        data: updatedHistory!
      }
    } catch (error) {
      console.error('Update answer error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

// 导出单例实例
export const chatHistoryService = ChatHistoryService.getInstance()
