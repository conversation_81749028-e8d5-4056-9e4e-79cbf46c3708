/**
 * 内容捕获器
 * 统一管理页面内容的捕获和提取逻辑
 */

import { Conversation, Message } from '@/types'
import { 
  IContentCapturer, 
  SelectorConfig, 
  ElementType, 
  PlatformConfig,
  MessageExtractionResult
} from '../types/adapters'
import { IEventBus, BusinessEvent } from '../types/events'
import { ILogger } from '../types/utils'
import { CommonRegexPatterns } from '../types/PlatformConfig'

/**
 * 内容捕获器实现
 */
export class ContentCapturer implements IContentCapturer {
  private eventBus: IEventBus
  private logger?: ILogger
  private platformConfig: PlatformConfig
  private extractionCache: Map<string, any> = new Map()
  private cacheExpiry: number = 5000 // 5秒缓存过期

  constructor(
    eventBus: IEventBus, 
    platformConfig: PlatformConfig,
    logger?: ILogger
  ) {
    this.eventBus = eventBus
    this.platformConfig = platformConfig
    this.logger = logger
  }

  /**
   * 捕获对话内容
   */
  async captureConversation(selectors: SelectorConfig): Promise<Conversation | null> {
    const cacheKey = `conversation_${window.location.href}_${Date.now()}`
    
    try {
      this.log('info', 'Starting conversation capture...')
      
      // 查找消息容器
      const messageContainer = this.findMessageContainer(selectors)
      if (!messageContainer) {
        this.log('warn', 'No message container found')
        return null
      }

      // 提取消息
      const messages = this.extractMessages(messageContainer, this.platformConfig)
      if (messages.length === 0) {
        this.log('warn', 'No messages found in container')
        return null
      }

      // 转换为标准消息格式
      const standardMessages: Message[] = messages.map((msg, index) => ({
        id: msg.element.getAttribute('data-message-id') || `msg-${index}`,
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp
      }))

      // 获取对话标题
      const title = this.extractConversationTitle(selectors)

      // 创建对话对象
      const conversation: Conversation = {
        id: `${this.platformConfig.id}-${Date.now()}`,
        platform: this.platformConfig.id,
        title: title || `${this.platformConfig.name}对话 - ${new Date().toLocaleDateString()}`,
        messages: standardMessages,
        createdAt: Math.min(...standardMessages.map(m => m.timestamp)),
        updatedAt: Math.max(...standardMessages.map(m => m.timestamp))
      }

      // 缓存结果
      this.cacheResult(cacheKey, conversation)

      // 发布捕获完成事件
      this.eventBus.publish(BusinessEvent.CONVERSATION_CAPTURED, {
        conversation,
        platform: this.platformConfig.id
      })

      this.log('info', `Conversation captured successfully: ${standardMessages.length} messages`)
      return conversation

    } catch (error) {
      this.log('error', 'Failed to capture conversation:', error)
      return null
    }
  }

  /**
   * 捕获页面元素
   */
  captureElements(selectors: SelectorConfig): Map<ElementType, HTMLElement> {
    const elements = new Map<ElementType, HTMLElement>()

    try {
      // 捕获各种类型的元素
      const elementTypes: Array<{ type: ElementType; selectors: string[] }> = [
        { type: ElementType.INPUT_FIELD, selectors: selectors.inputField },
        { type: ElementType.SEND_BUTTON, selectors: selectors.sendButton },
        { type: ElementType.MESSAGE_CONTAINER, selectors: selectors.messageContainer },
        { type: ElementType.INPUT_CONTAINER, selectors: selectors.inputContainer }
      ]

      elementTypes.forEach(({ type, selectors: selectorList }) => {
        const element = this.findFirstElement(selectorList)
        if (element) {
          elements.set(type, element)
          this.log('debug', `Captured ${type} element:`, element)
        }
      })

      this.log('info', `Captured ${elements.size} page elements`)
      return elements

    } catch (error) {
      this.log('error', 'Failed to capture page elements:', error)
      return elements
    }
  }

  /**
   * 提取消息内容
   */
  extractMessages(container: HTMLElement, config: PlatformConfig): MessageExtractionResult[] {
    const messages: MessageExtractionResult[] = []

    try {
      // 查找所有消息元素
      const messageElements = this.findMessageElements(container, config)
      
      messageElements.forEach((element, index) => {
        const messageResult = this.extractSingleMessage(element, config, index)
        if (messageResult) {
          messages.push(messageResult)
        }
      })

      this.log('info', `Extracted ${messages.length} messages from container`)
      return messages

    } catch (error) {
      this.log('error', 'Failed to extract messages:', error)
      return messages
    }
  }

  /**
   * 清理内容
   */
  cleanContent(content: string, patterns?: RegExp[]): string {
    if (!content) return ''

    let cleaned = content

    // 使用平台特定的清理模式
    if (this.platformConfig.regexPatterns?.contentCleaning) {
      cleaned = cleaned.replace(this.platformConfig.regexPatterns.contentCleaning, ' ')
    }

    // 使用额外的清理模式
    if (patterns) {
      patterns.forEach(pattern => {
        cleaned = cleaned.replace(pattern, ' ')
      })
    }

    // 使用通用清理模式
    cleaned = cleaned
      .replace(CommonRegexPatterns.HTML_TAGS, '') // 移除HTML标签
      .replace(CommonRegexPatterns.EXTRA_WHITESPACE, ' ') // 清理多余空白
      .trim()

    return cleaned
  }

  /**
   * 查找消息容器
   */
  private findMessageContainer(selectors: SelectorConfig): HTMLElement | null {
    return this.findFirstElement(selectors.messageContainer)
  }

  /**
   * 查找第一个匹配的元素
   */
  private findFirstElement(selectors: string[]): HTMLElement | null {
    for (const selector of selectors) {
      try {
        const element = document.querySelector(selector) as HTMLElement
        if (element) {
          return element
        }
      } catch (error) {
        this.log('warn', `Invalid selector: ${selector}`, error)
      }
    }
    return null
  }

  /**
   * 查找消息元素
   */
  private findMessageElements(container: HTMLElement, config: PlatformConfig): HTMLElement[] {
    const elements: HTMLElement[] = []

    // 尝试使用平台特定的选择器
    const messageSelectors = [
      ...config.selectors.messageContainer,
      '[data-message-author-role]',
      '.message',
      '.chat-message',
      '[role="article"]'
    ]

    for (const selector of messageSelectors) {
      try {
        const found = container.querySelectorAll(selector) as NodeListOf<HTMLElement>
        if (found.length > 0) {
          elements.push(...Array.from(found))
          break // 找到一组就停止
        }
      } catch (error) {
        this.log('warn', `Invalid message selector: ${selector}`, error)
      }
    }

    return elements
  }

  /**
   * 提取单个消息
   */
  private extractSingleMessage(
    element: HTMLElement, 
    config: PlatformConfig, 
    index: number
  ): MessageExtractionResult | null {
    try {
      // 判断消息角色
      const role = this.determineMessageRole(element, config)
      
      // 提取消息内容
      const content = this.extractMessageContent(element, config)
      if (!content) {
        return null
      }

      // 提取时间戳
      const timestamp = this.extractTimestamp(element) || (Date.now() - (index * 1000))

      return {
        role,
        content,
        timestamp,
        element
      }

    } catch (error) {
      this.log('warn', `Failed to extract message at index ${index}:`, error)
      return null
    }
  }

  /**
   * 判断消息角色
   */
  private determineMessageRole(element: HTMLElement, config: PlatformConfig): 'user' | 'assistant' {
    // 使用平台特定的识别模式
    if (config.regexPatterns?.userMessageIdentifier) {
      const text = element.textContent || ''
      if (config.regexPatterns.userMessageIdentifier.test(text)) {
        return 'user'
      }
    }

    if (config.regexPatterns?.assistantMessageIdentifier) {
      const text = element.textContent || ''
      if (config.regexPatterns.assistantMessageIdentifier.test(text)) {
        return 'assistant'
      }
    }

    // 使用通用的判断逻辑
    const userIndicators = [
      '[data-message-author-role="user"]',
      '.user-message',
      '.user',
      '[data-role="user"]'
    ]

    for (const indicator of userIndicators) {
      if (element.matches(indicator) || element.querySelector(indicator)) {
        return 'user'
      }
    }

    // 默认为助手消息
    return 'assistant'
  }

  /**
   * 提取消息内容
   */
  private extractMessageContent(element: HTMLElement, config: PlatformConfig): string {
    // 尝试使用平台特定的内容选择器
    const contentSelectors = [
      '.markdown',
      '.message-content',
      '.content',
      '[data-message-content]',
      '.prose'
    ]

    for (const selector of contentSelectors) {
      const contentElement = element.querySelector(selector)
      if (contentElement) {
        return this.cleanContent(contentElement.textContent || '')
      }
    }

    // 如果没有找到特定的内容元素，使用整个元素的文本
    return this.cleanContent(element.textContent || '')
  }

  /**
   * 提取时间戳
   */
  private extractTimestamp(element: HTMLElement): number | null {
    // 查找时间戳元素
    const timeSelectors = [
      'time',
      '[datetime]',
      '.timestamp',
      '.time'
    ]

    for (const selector of timeSelectors) {
      const timeElement = element.querySelector(selector)
      if (timeElement) {
        const datetime = timeElement.getAttribute('datetime')
        if (datetime) {
          const timestamp = new Date(datetime).getTime()
          if (!isNaN(timestamp)) {
            return timestamp
          }
        }

        // 尝试解析文本内容
        const timeText = timeElement.textContent || ''
        const match = timeText.match(CommonRegexPatterns.TIMESTAMP)
        if (match) {
          const timestamp = new Date(match[0]).getTime()
          if (!isNaN(timestamp)) {
            return timestamp
          }
        }
      }
    }

    return null
  }

  /**
   * 提取对话标题
   */
  private extractConversationTitle(selectors: SelectorConfig): string | null {
    const titleSelectors = [
      ...(selectors.conversationTitle || []),
      'h1',
      '.conversation-title',
      '.chat-title',
      '[data-testid="conversation-title"]'
    ]

    const titleElement = this.findFirstElement(titleSelectors)
    return titleElement ? this.cleanContent(titleElement.textContent || '') : null
  }

  /**
   * 缓存结果
   */
  private cacheResult(key: string, result: any): void {
    this.extractionCache.set(key, {
      data: result,
      timestamp: Date.now()
    })

    // 清理过期缓存
    this.cleanExpiredCache()
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now()
    for (const [key, value] of this.extractionCache.entries()) {
      if (now - value.timestamp > this.cacheExpiry) {
        this.extractionCache.delete(key)
      }
    }
  }

  /**
   * 记录日志
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: any): void {
    const fullMessage = `[ContentCapturer] ${message}`
    
    if (this.logger) {
      this.logger[level](fullMessage, context)
    } else {
      console[level](fullMessage, context)
    }
  }
}
