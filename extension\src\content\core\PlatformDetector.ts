import { PlatformConfig, PlatformDetectionResult } from '../types/PlatformConfig'
import { PlatformConfigList } from '../configs/platformConfigs'

/**
 * 平台检测器
 * 基于正则表达式检测当前页面所属的AI平台
 */
export class PlatformDetector {
  private static instance: PlatformDetector
  private detectionCache: Map<string, PlatformDetectionResult> = new Map()

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): PlatformDetector {
    if (!PlatformDetector.instance) {
      PlatformDetector.instance = new PlatformDetector()
    }
    return PlatformDetector.instance
  }

  /**
   * 检测当前页面的平台
   */
  detectCurrentPlatform(): PlatformDetectionResult {
    const currentUrl = window.location.href
    const hostname = window.location.hostname
    const pathname = window.location.pathname

    // 检查缓存
    const cacheKey = `${hostname}${pathname}`
    if (this.detectionCache.has(cacheKey)) {
      const cached = this.detectionCache.get(cacheKey)!
      console.log('【PlatformDetector】Using cached detection result:', cached)
      return cached
    }

    console.log('【PlatformDetector】Detecting platform for:', { hostname, pathname, currentUrl })

    let bestMatch: PlatformDetectionResult = {
      platform: null,
      confidence: 0,
      matchedPatterns: []
    }

    // 遍历所有平台配置进行匹配
    for (const config of PlatformConfigList) {
      const result = this.matchPlatform(config, hostname, pathname)
      
      if (result.confidence > bestMatch.confidence) {
        bestMatch = result
      }
      
      console.log(`【PlatformDetector】${config.name} match result:`, result)
    }

    // 缓存结果
    this.detectionCache.set(cacheKey, bestMatch)

    if (bestMatch.platform) {
      console.log(`【PlatformDetector】Detected platform: ${bestMatch.platform.name} (confidence: ${bestMatch.confidence})`)
    } else {
      console.log('【PlatformDetector】No platform detected')
    }

    return bestMatch
  }

  /**
   * 匹配单个平台
   */
  private matchPlatform(config: PlatformConfig, hostname: string, pathname: string): PlatformDetectionResult {
    const matchedPatterns: string[] = []
    let confidence = 0

    // 检查主机名匹配
    if (config.patterns.hostname.test(hostname)) {
      matchedPatterns.push('hostname')
      confidence += 50 // 主机名匹配基础分数
      
      // 检查路径匹配（如果配置了）
      if (config.patterns.validPath) {
        if (config.patterns.validPath.test(pathname)) {
          matchedPatterns.push('validPath')
          confidence += 30 // 路径匹配额外分数
        } else {
          // 主机名匹配但路径不匹配，降低置信度
          confidence -= 20
        }
      } else {
        // 没有路径限制，给予额外分数
        confidence += 20
      }

      // 检查页面元素存在性（提高置信度）
      const elementScore = this.checkElementsExistence(config)
      confidence += elementScore

      return {
        platform: config,
        confidence: Math.max(0, Math.min(100, confidence)), // 限制在0-100范围内
        matchedPatterns
      }
    }

    return {
      platform: null,
      confidence: 0,
      matchedPatterns: []
    }
  }

  /**
   * 检查关键元素是否存在以提高检测置信度
   */
  private checkElementsExistence(config: PlatformConfig): number {
    let score = 0
    
    // 检查输入框
    if (this.findAnyElement(config.selectors.inputField)) {
      score += 10
    }
    
    // 检查发送按钮
    if (this.findAnyElement(config.selectors.sendButton)) {
      score += 5
    }
    
    // 检查消息容器
    if (this.findAnyElement(config.selectors.messageContainer)) {
      score += 5
    }

    return score
  }

  /**
   * 查找任意一个匹配的元素
   */
  private findAnyElement(selectors: string[]): boolean {
    for (const selector of selectors) {
      try {
        if (document.querySelector(selector)) {
          return true
        }
      } catch (error) {
        // 忽略无效选择器错误
        console.warn('【PlatformDetector】Invalid selector:', selector, error)
      }
    }
    return false
  }

  /**
   * 根据URL检测平台（静态方法，用于快速检测）
   */
  static detectByUrl(url: string): PlatformConfig | null {
    const urlObj = new URL(url)
    const hostname = urlObj.hostname
    const pathname = urlObj.pathname

    for (const config of PlatformConfigList) {
      if (config.patterns.hostname.test(hostname)) {
        if (!config.patterns.validPath || config.patterns.validPath.test(pathname)) {
          return config
        }
      }
    }

    return null
  }

  /**
   * 检查指定平台是否匹配当前页面
   */
  isPlatformMatch(platformId: string): boolean {
    const result = this.detectCurrentPlatform()
    return result.platform?.id === platformId
  }

  /**
   * 获取所有支持的平台列表
   */
  getSupportedPlatforms(): PlatformConfig[] {
    return [...PlatformConfigList]
  }

  /**
   * 清除检测缓存
   */
  clearCache(): void {
    this.detectionCache.clear()
    console.log('【PlatformDetector】Detection cache cleared')
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.detectionCache.size,
      keys: Array.from(this.detectionCache.keys())
    }
  }
}
