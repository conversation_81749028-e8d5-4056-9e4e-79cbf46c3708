/**
 * 元素管理器
 * 负责 DOM 元素的查找、缓存和监听
 */

import { EventBus } from '../shared/EventBus'

export interface SelectorConfig {
  inputField: string[]
  sendButton: string[]
  messageContainer: string[]
  inputContainer: string[]
}

export type ElementType = keyof SelectorConfig

/**
 * 缓存的元素信息
 */
interface CachedElement {
  element: HTMLElement
  selector: string
  timestamp: number
  isValid: boolean
}

/**
 * 元素管理器类
 * 提供统一的 DOM 元素查找、缓存和监听功能
 */
export class ElementManager {
  private cache: Map<ElementType, CachedElement> = new Map()
  private observers: Map<ElementType, MutationObserver> = new Map()
  private eventBus: EventBus
  private cacheTimeout: number = 30000 // 30秒缓存超时
  private debugMode: boolean = false

  constructor(eventBus: EventBus, debugMode: boolean = false) {
    this.eventBus = eventBus
    this.debugMode = debugMode
  }

  /**
   * 获取元素
   * @param type 元素类型
   * @param selectors 选择器数组
   * @param forceRefresh 是否强制刷新缓存
   * @returns 找到的元素或 null
   */
  getElement(
    type: ElementType,
    selectors: string[],
    forceRefresh: boolean = false
  ): HTMLElement | null {
    // 检查缓存
    if (!forceRefresh) {
      const cached = this.getCachedElement(type)
      if (cached) {
        return cached
      }
    }

    // 查找元素
    const result = this.findElement(selectors)
    if (result.element) {
      // 缓存元素
      this.cacheElement(type, result.element, result.selector)
      
      // 设置监听器
      this.setupElementObserver(type, result.element)
      
      // 发布事件
      this.eventBus.publish('element-found', {
        type,
        element: result.element
      })

      if (this.debugMode) {
        console.log(`[ElementManager] Found ${type} element:`, result.element)
      }

      return result.element
    }

    if (this.debugMode) {
      console.warn(`[ElementManager] Element ${type} not found with selectors:`, selectors)
    }

    return null
  }

  /**
   * 查找元素
   * @param selectors 选择器数组
   * @returns 查找结果
   */
  private findElement(selectors: string[]): { element: HTMLElement | null; selector: string } {
    for (const selector of selectors) {
      try {
        const element = document.querySelector(selector) as HTMLElement
        if (element && this.isElementVisible(element)) {
          return { element, selector }
        }
      } catch (error) {
        if (this.debugMode) {
          console.warn(`[ElementManager] Invalid selector: ${selector}`, error)
        }
      }
    }
    return { element: null, selector: '' }
  }

  /**
   * 检查元素是否可见
   * @param element 要检查的元素
   * @returns 是否可见
   */
  private isElementVisible(element: HTMLElement): boolean {
    return element.offsetWidth > 0 && element.offsetHeight > 0
  }

  /**
   * 缓存元素
   * @param type 元素类型
   * @param element 元素
   * @param selector 选择器
   */
  private cacheElement(type: ElementType, element: HTMLElement, selector: string): void {
    this.cache.set(type, {
      element,
      selector,
      timestamp: Date.now(),
      isValid: true
    })
  }

  /**
   * 获取缓存的元素
   * @param type 元素类型
   * @returns 缓存的元素或 null
   */
  private getCachedElement(type: ElementType): HTMLElement | null {
    const cached = this.cache.get(type)
    if (!cached) {
      return null
    }

    // 检查缓存是否过期
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(type)
      return null
    }

    // 检查元素是否仍然有效
    if (!cached.isValid || !document.contains(cached.element) || !this.isElementVisible(cached.element)) {
      this.cache.delete(type)
      this.eventBus.publish('element-lost', { type })
      return null
    }

    return cached.element
  }

  /**
   * 设置元素观察器
   * @param type 元素类型
   * @param element 要观察的元素
   */
  private setupElementObserver(type: ElementType, element: HTMLElement): void {
    // 清除旧的观察器
    const oldObserver = this.observers.get(type)
    if (oldObserver) {
      oldObserver.disconnect()
    }

    // 创建新的观察器
    const observer = new MutationObserver((mutations) => {
      let shouldInvalidate = false

      mutations.forEach((mutation) => {
        // 检查元素是否被移除
        if (mutation.type === 'childList') {
          mutation.removedNodes.forEach((node) => {
            if (node === element || (node as Element).contains?.(element)) {
              shouldInvalidate = true
            }
          })
        }

        // 检查属性变化
        if (mutation.type === 'attributes' && mutation.target === element) {
          const target = mutation.target as HTMLElement
          if (!this.isElementVisible(target)) {
            shouldInvalidate = true
          }
        }
      })

      if (shouldInvalidate) {
        this.invalidateCache(type)
      }
    })

    // 观察元素的父容器
    const container = element.closest('body') || document.body
    observer.observe(container, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class', 'hidden']
    })

    this.observers.set(type, observer)
  }

  /**
   * 使缓存失效
   * @param type 元素类型
   */
  private invalidateCache(type: ElementType): void {
    const cached = this.cache.get(type)
    if (cached) {
      cached.isValid = false
      this.eventBus.publish('element-lost', { type })
      
      if (this.debugMode) {
        console.log(`[ElementManager] Cache invalidated for ${type}`)
      }
    }
  }

  /**
   * 清除指定类型的缓存
   * @param type 元素类型
   */
  clearCache(type?: ElementType): void {
    if (type) {
      this.cache.delete(type)
      const observer = this.observers.get(type)
      if (observer) {
        observer.disconnect()
        this.observers.delete(type)
      }
    } else {
      this.cache.clear()
      this.observers.forEach(observer => observer.disconnect())
      this.observers.clear()
    }

    if (this.debugMode) {
      console.log(`[ElementManager] Cache cleared for ${type || 'all types'}`)
    }
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  getCacheStats(): { [key: string]: any } {
    const stats: { [key: string]: any } = {}
    
    this.cache.forEach((cached, type) => {
      stats[type] = {
        selector: cached.selector,
        age: Date.now() - cached.timestamp,
        isValid: cached.isValid,
        isVisible: this.isElementVisible(cached.element)
      }
    })

    return stats
  }

  /**
   * 销毁元素管理器
   */
  destroy(): void {
    this.clearCache()
    
    if (this.debugMode) {
      console.log('[ElementManager] Destroyed')
    }
  }

  /**
   * 启用/禁用调试模式
   * @param enabled 是否启用
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled
  }
}
