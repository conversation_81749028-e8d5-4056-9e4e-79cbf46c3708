/**
 * 通用选择器配置
 * 包含跨平台通用的选择器模式
 */

import { SelectorConfig } from '../base/ElementManager'

/**
 * 通用选择器配置类
 * 提供所有平台都可能使用的通用选择器
 */
export class CommonSelectors {
  /**
   * 获取通用选择器配置
   * @returns 通用选择器配置对象
   */
  static getCommonSelectors(): SelectorConfig {
    return {
      inputField: [
        // 通用输入框选择器
        'textarea[placeholder*="message" i]',
        'textarea[placeholder*="prompt" i]',
        'textarea[placeholder*="输入" i]',
        'textarea[placeholder*="请输入" i]',
        'div[contenteditable="true"]',
        'input[type="text"]',
        '[role="textbox"]',
        // 常见的输入框类名
        '.input-field',
        '.chat-input',
        '.message-input',
        '.prompt-input'
      ],
      sendButton: [
        // 通用发送按钮选择器
        'button[aria-label*="send" i]',
        'button[aria-label*="发送" i]',
        'button[data-testid*="send" i]',
        'button[type="submit"]',
        'button:has(svg)',
        '[role="button"]:has(svg)',
        // 常见的发送按钮类名
        '.send-button',
        '.submit-button',
        '.send-btn',
        '.chat-send'
      ],
      messageContainer: [
        // 通用消息容器选择器
        '[data-message-author-role]',
        '[data-testid*="message"]',
        '.message',
        '.chat-message',
        '.conversation-item',
        '.message-item',
        '[role="article"]',
        // 常见的消息容器类名
        '.message-container',
        '.chat-container',
        '.conversation-container',
        '.messages'
      ],
      inputContainer: [
        // 通用输入容器选择器
        '.input-container',
        '.chat-input-container',
        '.message-input-container',
        '.prompt-container',
        '.editor-container',
        '.input-wrapper',
        '.chat-editor',
        '.input-area',
        // 表单相关
        'form',
        '.form-container',
        '.input-form'
      ]
    }
  }

  /**
   * 获取特定类型的通用选择器
   * @param type 选择器类型
   * @returns 指定类型的选择器数组
   */
  static getCommonSelectorsForType(type: keyof SelectorConfig): string[] {
    const commonSelectors = this.getCommonSelectors()
    return commonSelectors[type] || []
  }

  /**
   * 合并通用选择器和平台特定选择器
   * @param platformSelectors 平台特定选择器
   * @param prioritizePlatform 是否优先使用平台选择器
   * @returns 合并后的选择器配置
   */
  static mergeWithPlatformSelectors(
    platformSelectors: Partial<SelectorConfig>,
    prioritizePlatform: boolean = true
  ): SelectorConfig {
    const commonSelectors = this.getCommonSelectors()
    const merged: SelectorConfig = {
      inputField: [],
      sendButton: [],
      messageContainer: [],
      inputContainer: []
    }

    // 合并每种类型的选择器
    Object.keys(merged).forEach(key => {
      const selectorType = key as keyof SelectorConfig
      const common = commonSelectors[selectorType] || []
      const platform = platformSelectors[selectorType] || []

      if (prioritizePlatform) {
        // 平台特定选择器优先
        merged[selectorType] = [...platform, ...common]
      } else {
        // 通用选择器优先
        merged[selectorType] = [...common, ...platform]
      }
    })

    return merged
  }

  /**
   * 验证选择器是否有效
   * @param selector 要验证的选择器
   * @returns 是否有效
   */
  static isValidSelector(selector: string): boolean {
    try {
      document.querySelector(selector)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 过滤有效的选择器
   * @param selectors 选择器数组
   * @returns 有效的选择器数组
   */
  static filterValidSelectors(selectors: string[]): string[] {
    return selectors.filter(selector => this.isValidSelector(selector))
  }

  /**
   * 获取优化后的选择器配置
   * 移除无效的选择器，提高查找效率
   * @param config 原始选择器配置
   * @returns 优化后的选择器配置
   */
  static optimizeSelectors(config: SelectorConfig): SelectorConfig {
    return {
      inputField: this.filterValidSelectors(config.inputField),
      sendButton: this.filterValidSelectors(config.sendButton),
      messageContainer: this.filterValidSelectors(config.messageContainer),
      inputContainer: this.filterValidSelectors(config.inputContainer)
    }
  }
}

/**
 * 导出通用选择器配置实例
 */
export const commonSelectors = CommonSelectors.getCommonSelectors()
