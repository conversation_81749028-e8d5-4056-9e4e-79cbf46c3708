import { BaseAIAdapter } from '../base/BaseAIAdapter'
import { Conversation, Message } from '@/types'
import { GeminiConfig } from '../configs/platformConfigs'
import { SelectorConfig } from '../base/ElementManager'

export class GeminiAdapter extends BaseAIAdapter {
  constructor() {
    super(GeminiConfig)
  }

  /**
   * 获取 Gemini 平台特定的选择器配置
   */
  getSelectors(): SelectorConfig {
    return {
      inputField: [
        'rich-textarea[placeholder*="Enter a prompt"]',
        'textarea[aria-label*="Message"]'
      ],
      sendButton: [
        'button[aria-label*="Send"]',
        'button[data-testid="send-button"]'
      ],
      messageContainer: [
        'message-content',
        '.conversation-container'
      ],
      inputContainer: [
        '.input-container',
        '.prompt-container',
        '.composer-parent'
      ]
    }
  }



  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = this.findElements(this.config.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []

      messageElements.forEach((element, index) => {
        // Gemini的消息结构可能比较复杂
        const isUser = element.querySelector('[data-test-id="user-message"]') !== null ||
                      element.classList.contains('user-message') ||
                      element.getAttribute('data-message-author') === 'user'

        const contentElement = element.querySelector('[data-test-id="message-content"]') ||
                              element.querySelector('.message-content') ||
                              element.querySelector('.markdown-content') ||
                              element

        if (contentElement) {
          const content = this.cleanContent(contentElement.textContent || '')
          if (content && content.length > 0) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      // 使用配置的标题选择器
      const titleElement = this.findElement(this.config.selectors.conversationTitle || ['.conversation-title'])
      const title = titleElement.element?.textContent || `Gemini对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `gemini-${Date.now()}`,
        platform: 'gemini',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('【EchoSync】Extract Gemini conversation error:', error)
      return null
    }
  }

  /**
   * 提取最新的AI回答
   */
  protected async extractLatestAnswer(): Promise<string | null> {
    try {
      // 查找最新的AI回答内容
      const answerElements = this.findElements([
        '[data-test-id="message-content"]',
        '.message-content',
        '.markdown-content',
        ...(this.config.selectors.assistantMessage || [])
      ])

      if (answerElements.length === 0) return null

      // 获取最后一个答案（排除用户消息）
      for (let i = answerElements.length - 1; i >= 0; i--) {
        const element = answerElements[i]
        const isUserMessage = element.closest('[data-test-id="user-message"]') !== null

        if (!isUserMessage) {
          const content = this.cleanContent(element.textContent || '')
          if (content.length > 0) {
            console.log('【EchoSync】Gemini extracted answer:', content.substring(0, 100) + '...')
            return content
          }
        }
      }

      return null
    } catch (error) {
      console.error('【EchoSync】Extract Gemini latest answer error:', error)
      return null
    }
  }
}
