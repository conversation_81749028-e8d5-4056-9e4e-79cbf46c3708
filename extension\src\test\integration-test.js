/**
 * Answer存储功能集成测试脚本
 * 测试完整的用户工作流程
 */

class IntegrationTest {
  constructor() {
    this.testScenarios = []
    this.currentScenario = null
  }

  /**
   * 运行集成测试
   */
  async runIntegrationTests() {
    console.group('【EchoSync】集成测试开始')
    
    try {
      await this.testCompleteWorkflow()
      await this.testCrossPlatformWorkflow()
      await this.testErrorHandling()
      await this.testDataConsistency()
      
      this.printIntegrationResults()
    } catch (error) {
      console.error('集成测试失败:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 测试完整工作流程
   */
  async testCompleteWorkflow() {
    this.currentScenario = '完整工作流程'
    console.log('🔄 集成测试1: 完整工作流程')
    
    try {
      const testPrompt = '集成测试提示词 - ' + Date.now()
      const testAnswer = '这是AI的回答内容 - ' + Date.now()
      
      // 步骤1: 用户输入提示词并存档
      const archiveButton = new ArchiveButton()
      await archiveButton.archiveCurrentPrompt(
        () => testPrompt,
        { id: 1, name: 'Test Platform' },
        (msg, type) => console.log(`通知: ${msg} (${type})`)
      )
      
      // 步骤2: 验证提示词已存储
      const promptResult = await chatPromptService.findByPrompt(testPrompt)
      if (!promptResult.success || !promptResult.data) {
        throw new Error('提示词存储失败')
      }
      
      const chatUid = promptResult.data.chat_uid
      
      // 步骤3: 模拟AI回答并存储
      await chatHistoryService.create({
        chat_answer: testAnswer,
        chat_uid: chatUid,
        platform_id: 1
      })
      
      // 步骤4: 验证完整对话信息
      const fullChatResult = await chatHistoryService.getFullChatInfo(chatUid)
      if (!fullChatResult.success || !fullChatResult.data) {
        throw new Error('获取完整对话信息失败')
      }
      
      // 步骤5: 测试历史记录显示
      const historyResult = await chatHistoryDatabaseProxy.getUniqueChats({ limit: 10 })
      const foundChat = historyResult.data?.find(chat => chat.chat_uid === chatUid)
      
      if (foundChat) {
        this.addScenarioResult(true, '完整工作流程测试通过')
        console.log('✅ 完整工作流程测试成功')
      } else {
        this.addScenarioResult(false, '历史记录中未找到对话')
      }
      
    } catch (error) {
      this.addScenarioResult(false, '工作流程异常: ' + error.message)
    }
  }

  /**
   * 测试跨平台工作流程
   */
  async testCrossPlatformWorkflow() {
    this.currentScenario = '跨平台工作流程'
    console.log('🔄 集成测试2: 跨平台工作流程')
    
    try {
      const testPrompt = '跨平台测试提示词 - ' + Date.now()
      
      // 步骤1: 在平台A存储提示词
      const archiveButton1 = new ArchiveButton()
      await archiveButton1.autoArchivePrompt(testPrompt, { id: 1, name: 'Platform A' })
      
      const promptResult = await chatPromptService.findByPrompt(testPrompt)
      const chatUid = promptResult.data.chat_uid
      
      // 步骤2: 在平台B复用相同提示词
      const archiveButton2 = new ArchiveButton()
      await archiveButton2.autoArchivePrompt(testPrompt, { id: 2, name: 'Platform B' })
      
      // 步骤3: 验证两个平台使用相同的chat_uid
      const historyResult = await chatHistoryService.getList({ chat_uid: chatUid })
      const platforms = new Set(historyResult.data?.data.map(h => h.platform_id) || [])
      
      if (platforms.size >= 2) {
        this.addScenarioResult(true, '跨平台提示词共享成功')
        console.log('✅ 跨平台工作流程测试成功')
      } else {
        this.addScenarioResult(false, '跨平台共享失败，平台数量: ' + platforms.size)
      }
      
    } catch (error) {
      this.addScenarioResult(false, '跨平台工作流程异常: ' + error.message)
    }
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    this.currentScenario = '错误处理'
    console.log('🔄 集成测试3: 错误处理')
    
    try {
      let errorsCaught = 0
      
      // 测试1: 空提示词处理
      try {
        const archiveButton = new ArchiveButton()
        await archiveButton.archiveCurrentPrompt(
          () => '',
          { id: 1, name: 'Test Platform' },
          (msg, type) => {
            if (type === 'error' && msg.includes('空')) {
              errorsCaught++
            }
          }
        )
      } catch (error) {
        errorsCaught++
      }
      
      // 测试2: 无效平台处理
      try {
        const archiveButton = new ArchiveButton()
        await archiveButton.autoArchivePrompt('测试', null)
      } catch (error) {
        errorsCaught++
      }
      
      // 测试3: 数据库错误处理
      try {
        await chatHistoryService.create({
          chat_uid: 'invalid-uid',
          platform_id: 999999 // 不存在的平台ID
        })
      } catch (error) {
        errorsCaught++
      }
      
      if (errorsCaught >= 2) {
        this.addScenarioResult(true, `错误处理正常，捕获${errorsCaught}个错误`)
        console.log('✅ 错误处理测试通过')
      } else {
        this.addScenarioResult(false, `错误处理不足，仅捕获${errorsCaught}个错误`)
      }
      
    } catch (error) {
      this.addScenarioResult(false, '错误处理测试异常: ' + error.message)
    }
  }

  /**
   * 测试数据一致性
   */
  async testDataConsistency() {
    this.currentScenario = '数据一致性'
    console.log('🔄 集成测试4: 数据一致性')
    
    try {
      // 获取所有提示词
      const promptsResult = await chatPromptService.getList({ limit: 100 })
      const prompts = promptsResult.data?.data || []
      
      // 获取所有历史记录
      const historyResult = await chatHistoryService.getList({ limit: 1000 })
      const histories = historyResult.data?.data || []
      
      // 检查数据一致性
      let consistencyIssues = 0
      const promptUids = new Set(prompts.map(p => p.chat_uid))
      const historyUids = new Set(histories.map(h => h.chat_uid))
      
      // 检查孤立的历史记录
      for (const uid of historyUids) {
        if (!promptUids.has(uid)) {
          consistencyIssues++
          console.warn('发现孤立的历史记录:', uid)
        }
      }
      
      // 检查未使用的提示词
      for (const uid of promptUids) {
        if (!historyUids.has(uid)) {
          console.info('发现未使用的提示词:', uid)
        }
      }
      
      if (consistencyIssues === 0) {
        this.addScenarioResult(true, '数据一致性检查通过')
        console.log('✅ 数据一致性测试通过')
      } else {
        this.addScenarioResult(false, `发现${consistencyIssues}个一致性问题`)
      }
      
    } catch (error) {
      this.addScenarioResult(false, '数据一致性测试异常: ' + error.message)
    }
  }

  /**
   * 添加场景测试结果
   */
  addScenarioResult(passed, message) {
    this.testScenarios.push({
      scenario: this.currentScenario,
      passed,
      message,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 打印集成测试结果
   */
  printIntegrationResults() {
    console.group('🎯 集成测试结果汇总')
    
    const passedScenarios = this.testScenarios.filter(s => s.passed).length
    const totalScenarios = this.testScenarios.length
    
    console.log(`总场景数: ${totalScenarios}`)
    console.log(`通过场景: ${passedScenarios}`)
    console.log(`失败场景: ${totalScenarios - passedScenarios}`)
    console.log(`成功率: ${((passedScenarios / totalScenarios) * 100).toFixed(1)}%`)
    
    console.table(this.testScenarios)
    
    // 生成测试报告
    const report = {
      summary: {
        total: totalScenarios,
        passed: passedScenarios,
        failed: totalScenarios - passedScenarios,
        successRate: ((passedScenarios / totalScenarios) * 100).toFixed(1) + '%'
      },
      scenarios: this.testScenarios,
      timestamp: new Date().toISOString(),
      environment: {
        userAgent: navigator.userAgent,
        url: window.location.href
      }
    }
    
    console.log('📋 详细测试报告:', report)
    
    console.groupEnd()
    
    return report
  }
}

// 导出集成测试类
window.IntegrationTest = IntegrationTest

// 使用说明
console.log(`
【EchoSync】集成测试脚本已加载

使用方法:
const integrationTest = new IntegrationTest()
const report = await integrationTest.runIntegrationTests()

注意: 请在实际的AI平台页面上运行以获得最准确的测试结果
`)
