import { PlatformDetector } from './core/PlatformDetector'
import { MessageHandler } from './shared/BackgroundHandler'
import { ChatGPTAdapter } from './adapters/chatgpt'
import { DeepSeekAdapter } from './adapters/deepseek'
import { ClaudeAdapter } from './adapters/claude'
import { GeminiAdapter } from './adapters/gemini'
import { KimiAdapter } from './adapters/kimi'
import { BaseAIAdapter } from './base/BaseAIAdapter'
import { MessagingService } from '@/lib/service/messagingService'
import { MessageType } from '@/types'

console.log('【EchoSync】EchoSync Content Script loaded on:', window.location.href)
console.log('【EchoSync】Current hostname:', window.location.hostname)
console.log('【EchoSync】User agent:', navigator.userAgent)

/**
 * Content Script 管理器
 * 重构后以 BaseAIAdapter 为核心应用上下文，统一管理组件间交互
 */
class ContentScriptManager {
  private adapter: BaseAIAdapter | null = null
  private messageHandler: MessageHandler
  private platformDetector: PlatformDetector
  private isInitialized: boolean = false

  constructor() {
    this.platformDetector = PlatformDetector.getInstance()
    this.messageHandler = new MessageHandler()
    this.init()
  }

  /**
   * 初始化管理器
   * 重构后以 BaseAIAdapter 为核心应用上下文
   */
  async init() {
    if (this.isInitialized) {
      console.log('【EchoSync】ContentScriptManager already initialized, skipping')
      return
    }

    console.log('【EchoSync】----------- ContentScriptManager initializing... -----------')

    try {
      // 1. 检测当前平台
      const detectionResult = this.platformDetector.detectCurrentPlatform()

      if (detectionResult.platform) {
        console.log('【EchoSync】Detected platform:', detectionResult.platform.name, 'confidence:', detectionResult.confidence)

        // 2. 创建适配器作为核心应用上下文
        this.adapter = this.createAdapter(detectionResult.platform.id)

        if (this.adapter) {
          console.log('【EchoSync】BaseAIAdapter created as core application context')

          // 3. 依赖注入：将适配器注入到其他组件
          this.injectAdapterDependencies()

          // 4. 加载平台信息
          await this.loadPlatformInfo()

          // 5. 初始化适配器（这会初始化所有依赖的组件）
          await this.adapter.initialize()

          this.isInitialized = true
          console.log('【EchoSync】----------- ContentScriptManager initialization complete! -----------')
          console.log('【EchoSync】All components initialized with BaseAIAdapter as core context')
        }
      } else {
        console.log('【EchoSync】No supported platform detected')
      }
    } catch (error) {
      console.error('【EchoSync】ContentScriptManager initialization error:', error)
    }
  }

  /**
   * 依赖注入：将适配器实例注入到其他组件
   */
  private injectAdapterDependencies(): void {
    if (!this.adapter) return

    // 设置消息处理器的适配器
    this.messageHandler.setAdapter(this.adapter)
    console.log('【EchoSync】Adapter injected into MessageHandler')

    // 注意：ArchiveButton 和 InputManager 已经在 BaseAIAdapter 构造函数中
    // 通过构造函数注入了适配器实例，这里不需要额外处理

    console.log('【EchoSync】Dependency injection completed')
  }

  /**
   * 创建适配器实例作为核心应用上下文
   * BaseAIAdapter 将作为 content 模块的核心，统一管理：
   * - 事件总线（EventBus）
   * - 元素管理器（ElementManager）
   * - UI组件（ArchiveButton, InputManager, FloatingBubble 等）
   * - 平台特定的选择器配置
   */
  private createAdapter(platformId: string): BaseAIAdapter | null {
    console.log('【EchoSync】Creating BaseAIAdapter as core application context for platform:', platformId)

    switch (platformId) {
      case 'chatgpt':
        return new ChatGPTAdapter()
      case 'deepseek':
        return new DeepSeekAdapter()
      case 'claude':
        return new ClaudeAdapter()
      case 'gemini':
        return new GeminiAdapter()
      case 'kimi':
        return new KimiAdapter()
      default:
        console.warn('【EchoSync】Unknown platform ID:', platformId)
        return null
    }
  }

  /**
   * 加载平台信息
   * 重构后通过 MessagingService 与 Background 通信
   */
  private async loadPlatformInfo(): Promise<void> {
    if (!this.adapter) return

    try {
      // 通过 MessagingService 获取平台信息
      const platformsResult = await MessagingService.sendToBackground(MessageType.DB_PLATFORM_GET_LIST)

      if (platformsResult.success) {
        const config = this.adapter.getConfig()
        const currentPlatform = platformsResult.data.find((p: any) => {
          const nameMatch = p.name === config.name
          const urlMatch = window.location.href.includes(p.url.replace('https://', '').replace('http://', ''))
          return nameMatch || urlMatch
        }) || null

        this.adapter.setCurrentPlatform(currentPlatform)
        console.log('【EchoSync】Platform info loaded via MessagingService:', currentPlatform)
      } else {
        console.warn('【EchoSync】Failed to load platform info:', platformsResult.error)
      }
    } catch (error) {
      console.error('【EchoSync】Error loading platform info:', error)
    }
  }

  /**
   * 销毁管理器
   * 清理核心应用上下文和所有依赖组件
   */
  destroy(): void {
    console.log('【EchoSync】Destroying ContentScriptManager and core application context...')

    // 销毁核心应用上下文（BaseAIAdapter）
    // 这会自动清理所有依赖的组件：EventBus, ElementManager, UI组件等
    if (this.adapter) {
      this.adapter.destroy()
      this.adapter = null
      console.log('【EchoSync】Core application context (BaseAIAdapter) destroyed')
    }

    // 清理其他组件
    this.messageHandler.destroy()
    this.platformDetector.clearCache()
    this.isInitialized = false

    console.log('【EchoSync】ContentScriptManager destroyed completely')
  }


}

// 全局管理器实例
let globalManager: ContentScriptManager | null = null

/**
 * 初始化或重新初始化管理器
 */
function initializeManager(): void {
  // 销毁现有管理器
  if (globalManager) {
    globalManager.destroy()
    globalManager = null
  }

  // 创建新的管理器
  globalManager = new ContentScriptManager()
}

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeManager)
} else {
  initializeManager()
}

// 处理SPA路由变化
let lastUrl = location.href
const urlObserver = new MutationObserver(() => {
  const url = location.href
  if (url !== lastUrl) {
    lastUrl = url
    console.log('【EchoSync】URL changed, reinitializing...', url)

    // 延迟重新初始化，等待页面渲染完成
    setTimeout(() => {
      initializeManager()
    }, 1000)
  }
})

urlObserver.observe(document, { subtree: true, childList: true })

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
  if (globalManager) {
    globalManager.destroy()
    globalManager = null
  }
  urlObserver.disconnect()
})
