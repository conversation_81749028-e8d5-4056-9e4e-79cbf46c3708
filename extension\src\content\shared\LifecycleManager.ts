/**
 * 组件生命周期管理器
 * 统一管理所有组件和注入器的生命周期
 */

import { 
  IComponent, 
  IInject, 
  IComponentManager, 
  IInjectManager,
  ComponentLifecycleState 
} from '../types/components'
import { IEventBus } from '../types/events'
import { ILogger } from '../types/utils'

/**
 * 组件管理器实现
 */
export class ComponentManager implements IComponentManager {
  private components: Map<string, IComponent> = new Map()
  private logger?: ILogger

  constructor(logger?: ILogger) {
    this.logger = logger
  }

  /**
   * 注册组件
   */
  registerComponent(component: IComponent): void {
    if (this.components.has(component.id)) {
      throw new Error(`Component with ID ${component.id} already registered`)
    }

    this.components.set(component.id, component)
    this.log('info', `Component registered: ${component.name} (${component.id})`)
  }

  /**
   * 注销组件
   */
  unregisterComponent(id: string): void {
    const component = this.components.get(id)
    if (component) {
      // 销毁组件
      if (component.getState() !== ComponentLifecycleState.DESTROYED) {
        component.destroy()
      }
      
      this.components.delete(id)
      this.log('info', `Component unregistered: ${component.name} (${id})`)
    }
  }

  /**
   * 获取组件
   */
  getComponent<T extends IComponent>(id: string): T | null {
    return (this.components.get(id) as T) || null
  }

  /**
   * 获取所有组件
   */
  getAllComponents(): IComponent[] {
    return Array.from(this.components.values())
  }

  /**
   * 销毁所有组件
   */
  destroyAll(): void {
    const componentIds = Array.from(this.components.keys())
    
    for (const id of componentIds) {
      this.unregisterComponent(id)
    }
    
    this.log('info', `All components destroyed (${componentIds.length} components)`)
  }

  /**
   * 获取组件统计信息
   */
  getStats(): {
    total: number
    byState: Record<ComponentLifecycleState, number>
    byType: Record<string, number>
  } {
    const stats = {
      total: this.components.size,
      byState: {} as Record<ComponentLifecycleState, number>,
      byType: {} as Record<string, number>
    }

    // 初始化状态统计
    Object.values(ComponentLifecycleState).forEach(state => {
      stats.byState[state] = 0
    })

    // 统计组件
    this.components.forEach(component => {
      // 按状态统计
      stats.byState[component.getState()]++
      
      // 按类型统计
      const type = component.constructor.name
      stats.byType[type] = (stats.byType[type] || 0) + 1
    })

    return stats
  }

  private log(level: 'info' | 'warn' | 'error', message: string, context?: any): void {
    if (this.logger) {
      this.logger[level](`[ComponentManager] ${message}`, context)
    }
  }
}

/**
 * 注入器管理器实现
 */
export class InjectManager implements IInjectManager {
  private injects: Map<string, IInject> = new Map()
  private logger?: ILogger

  constructor(logger?: ILogger) {
    this.logger = logger
  }

  /**
   * 注册注入器
   */
  registerInject(inject: IInject): void {
    if (this.injects.has(inject.id)) {
      throw new Error(`Inject with ID ${inject.id} already registered`)
    }

    this.injects.set(inject.id, inject)
    this.log('info', `Inject registered: ${inject.name} (${inject.id})`)
  }

  /**
   * 注销注入器
   */
  unregisterInject(id: string): void {
    const inject = this.injects.get(id)
    if (inject) {
      // 销毁注入器
      if (inject.getState() !== ComponentLifecycleState.DESTROYED) {
        inject.destroy()
      }
      
      this.injects.delete(id)
      this.log('info', `Inject unregistered: ${inject.name} (${id})`)
    }
  }

  /**
   * 获取注入器
   */
  getInject(id: string): IInject | null {
    return this.injects.get(id) || null
  }

  /**
   * 获取所有注入器
   */
  getAllInjects(): IInject[] {
    return Array.from(this.injects.values())
  }

  /**
   * 执行所有注入
   */
  async injectAll(): Promise<void> {
    const injects = Array.from(this.injects.values())
    const results = await Promise.allSettled(
      injects.map(inject => inject.inject())
    )

    let successCount = 0
    let failureCount = 0

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successCount++
      } else {
        failureCount++
        this.log('error', `Failed to inject ${injects[index].name}:`, result.reason)
      }
    })

    this.log('info', `Inject all completed: ${successCount} success, ${failureCount} failed`)
  }

  /**
   * 取消所有注入
   */
  async uninjectAll(): Promise<void> {
    const injects = Array.from(this.injects.values())
    const results = await Promise.allSettled(
      injects.map(inject => inject.uninject())
    )

    let successCount = 0
    let failureCount = 0

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successCount++
      } else {
        failureCount++
        this.log('error', `Failed to uninject ${injects[index].name}:`, result.reason)
      }
    })

    this.log('info', `Uninject all completed: ${successCount} success, ${failureCount} failed`)
  }

  /**
   * 销毁所有注入器
   */
  destroyAll(): void {
    const injectIds = Array.from(this.injects.keys())
    
    for (const id of injectIds) {
      this.unregisterInject(id)
    }
    
    this.log('info', `All injects destroyed (${injectIds.length} injects)`)
  }

  /**
   * 获取注入器统计信息
   */
  getStats(): {
    total: number
    injected: number
    byState: Record<ComponentLifecycleState, number>
    byType: Record<string, number>
  } {
    const stats = {
      total: this.injects.size,
      injected: 0,
      byState: {} as Record<ComponentLifecycleState, number>,
      byType: {} as Record<string, number>
    }

    // 初始化状态统计
    Object.values(ComponentLifecycleState).forEach(state => {
      stats.byState[state] = 0
    })

    // 统计注入器
    this.injects.forEach(inject => {
      // 统计已注入数量
      if (inject.isInjected) {
        stats.injected++
      }
      
      // 按状态统计
      stats.byState[inject.getState()]++
      
      // 按类型统计
      const type = inject.constructor.name
      stats.byType[type] = (stats.byType[type] || 0) + 1
    })

    return stats
  }

  private log(level: 'info' | 'warn' | 'error', message: string, context?: any): void {
    if (this.logger) {
      this.logger[level](`[InjectManager] ${message}`, context)
    }
  }
}

/**
 * 生命周期管理器
 * 统一管理组件和注入器的生命周期
 */
export class LifecycleManager {
  private componentManager: ComponentManager
  private injectManager: InjectManager
  private eventBus: IEventBus
  private logger?: ILogger

  constructor(eventBus: IEventBus, logger?: ILogger) {
    this.eventBus = eventBus
    this.logger = logger
    this.componentManager = new ComponentManager(logger)
    this.injectManager = new InjectManager(logger)
  }

  /**
   * 获取组件管理器
   */
  getComponentManager(): IComponentManager {
    return this.componentManager
  }

  /**
   * 获取注入器管理器
   */
  getInjectManager(): IInjectManager {
    return this.injectManager
  }

  /**
   * 初始化所有组件和注入器
   */
  async initialize(): Promise<void> {
    try {
      this.log('info', 'Initializing lifecycle manager...')
      
      // 执行所有注入
      await this.injectManager.injectAll()
      
      this.log('info', 'Lifecycle manager initialized successfully')
    } catch (error) {
      this.log('error', 'Failed to initialize lifecycle manager:', error)
      throw error
    }
  }

  /**
   * 销毁所有组件和注入器
   */
  async destroy(): Promise<void> {
    try {
      this.log('info', 'Destroying lifecycle manager...')
      
      // 取消所有注入
      await this.injectManager.uninjectAll()
      
      // 销毁所有注入器
      this.injectManager.destroyAll()
      
      // 销毁所有组件
      this.componentManager.destroyAll()
      
      this.log('info', 'Lifecycle manager destroyed successfully')
    } catch (error) {
      this.log('error', 'Error during lifecycle manager destruction:', error)
    }
  }

  /**
   * 获取整体统计信息
   */
  getOverallStats(): {
    components: ReturnType<ComponentManager['getStats']>
    injects: ReturnType<InjectManager['getStats']>
  } {
    return {
      components: this.componentManager.getStats(),
      injects: this.injectManager.getStats()
    }
  }

  private log(level: 'info' | 'warn' | 'error', message: string, context?: any): void {
    if (this.logger) {
      this.logger[level](`[LifecycleManager] ${message}`, context)
    }
  }
}
