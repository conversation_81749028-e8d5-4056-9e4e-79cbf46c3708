/**
 * 事件相关类型定义
 * 统一管理所有事件相关的类型和接口
 */

import { ContentEventMap, ContentEventType, EventPriority } from '../shared/EventEnum'

/**
 * 事件元数据接口
 */
export interface EventMetadata {
  /** 事件ID */
  id: string
  /** 事件时间戳 */
  timestamp: number
  /** 事件优先级 */
  priority: EventPriority
  /** 事件来源 */
  source?: string
  /** 事件标签 */
  tags?: string[]
  /** 是否可取消 */
  cancelable?: boolean
  /** 是否已取消 */
  cancelled?: boolean
}

/**
 * 完整的事件对象接口
 */
export interface ContentEvent<T = any> {
  /** 事件类型 */
  type: ContentEventType
  /** 事件数据 */
  data: T
  /** 事件元数据 */
  metadata: EventMetadata
}

/**
 * 事件回调函数类型
 */
export type EventCallback<T = any> = (event: ContentEvent<T>) => void | Promise<void>

/**
 * 事件监听器配置
 */
export interface EventListenerConfig {
  /** 是否只监听一次 */
  once?: boolean
  /** 事件优先级过滤 */
  minPriority?: EventPriority
  /** 事件标签过滤 */
  tags?: string[]
  /** 超时时间（毫秒） */
  timeout?: number
}

/**
 * 事件监听器信息
 */
export interface ListenerInfo {
  callback: EventCallback
  config: EventListenerConfig
  addedAt: number
  callCount: number
  lastCalledAt?: number
  errors: Error[]
}

/**
 * 事件统计信息
 */
export interface EventStats {
  publishCount: number
  listenerCount: number
  errorCount: number
  averageExecutionTime: number
  lastPublishedAt?: number
}

/**
 * 事件总线配置选项
 */
export interface EventBusOptions {
  /** 是否启用调试模式 */
  debugMode?: boolean
  /** 最大监听器数量 */
  maxListeners?: number
  /** 是否启用性能监控 */
  performanceMonitoring?: boolean
}

/**
 * 输入事件数据类型
 */
export interface InputEventData {
  element: HTMLElement
  value: string
  previousValue?: string
  position?: { x: number; y: number }
  selector?: string
}

/**
 * 页面事件数据类型
 */
export interface PageEventData {
  url: string
  oldUrl?: string
  newUrl?: string
  loadTime?: number
  mutation?: MutationRecord
  mutations?: MutationRecord[]
}

/**
 * 元素事件数据类型
 */
export interface ElementEventData {
  type: string
  element?: HTMLElement
  selector: string
  oldState?: any
  newState?: any
  oldPosition?: DOMRect
  newPosition?: DOMRect
}

/**
 * UI事件数据类型
 */
export interface UIEventData {
  position?: { x: number; y: number }
  from?: { x: number; y: number }
  to?: { x: number; y: number }
  reason?: string
  content?: string
  timestamp?: number
  type?: string
  data?: any
  result?: any
}

/**
 * 业务事件数据类型
 */
export interface BusinessEventData {
  content?: string
  promptId?: string
  success?: boolean
  error?: Error
  retryCount?: number
  conversation?: any
  platform?: string
  source?: HTMLElement
  metadata?: any
}

/**
 * 适配器事件数据类型
 */
export interface AdapterEventData {
  platform: string
  config?: any
  oldConfig?: any
  newConfig?: any
  confidence?: number
  reason?: string
}

/**
 * 系统事件数据类型
 */
export interface SystemEventData {
  message?: string
  error?: Error
  context?: string
  stack?: string
  data?: any
}

/**
 * 事件数据联合类型
 */
export type EventData = 
  | InputEventData
  | PageEventData
  | ElementEventData
  | UIEventData
  | BusinessEventData
  | AdapterEventData
  | SystemEventData

/**
 * 类型安全的事件发布器接口
 */
export interface EventPublisher {
  publish<K extends keyof ContentEventMap>(
    event: K,
    data: ContentEventMap[K],
    metadata?: Partial<EventMetadata>
  ): void
}

/**
 * 类型安全的事件订阅器接口
 */
export interface EventSubscriber {
  subscribe<K extends keyof ContentEventMap>(
    event: K,
    callback: EventCallback<ContentEventMap[K]>,
    config?: EventListenerConfig
  ): () => void
  
  once<K extends keyof ContentEventMap>(
    event: K,
    callback: EventCallback<ContentEventMap[K]>,
    config?: EventListenerConfig
  ): () => void
  
  unsubscribe<K extends keyof ContentEventMap>(
    event: K,
    callback: EventCallback<ContentEventMap[K]>
  ): void
}

/**
 * 完整的事件总线接口
 */
export interface IEventBus extends EventPublisher, EventSubscriber {
  /** 获取监听器数量 */
  getListenerCount(event: string): number
  
  /** 获取所有事件名称 */
  getEventNames(): string[]
  
  /** 获取事件统计信息 */
  getEventStats(event: string): EventStats | undefined
  
  /** 获取所有事件统计信息 */
  getAllEventStats(): Map<string, EventStats>
  
  /** 获取监听器信息 */
  getListenerInfo(event: string): ListenerInfo[]
  
  /** 清除所有监听器 */
  clear(): void
  
  /** 清除指定事件的监听器 */
  clearEvent(event: string): void
  
  /** 设置调试模式 */
  setDebugMode(enabled: boolean): void
  
  /** 设置性能监控 */
  setPerformanceMonitoring(enabled: boolean): void
  
  /** 设置最大监听器数量 */
  setMaxListeners(max: number): void
}

/**
 * 事件过滤器函数类型
 */
export type EventFilter<T = any> = (event: ContentEvent<T>) => boolean

/**
 * 事件转换器函数类型
 */
export type EventTransformer<T = any, R = any> = (event: ContentEvent<T>) => ContentEvent<R>

/**
 * 事件中间件函数类型
 */
export type EventMiddleware = (
  event: ContentEvent,
  next: () => void | Promise<void>
) => void | Promise<void>
