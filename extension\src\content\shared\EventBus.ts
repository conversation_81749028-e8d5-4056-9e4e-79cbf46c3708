/**
 * 重构后的事件总线系统
 * 提供类型安全的事件订阅、发布、取消订阅功能
 * 支持事件优先级、元数据、错误处理和性能监控
 */

import {
  ContentEventType,
  ContentEventMap,
  ContentEvent,
  EventCallback,
  EventMetadata,
  EventPriority,
  EventListenerConfig
} from './EventEnum'

/**
 * 事件监听器信息
 */
interface ListenerInfo {
  callback: EventCallback
  config: EventListenerConfig
  addedAt: number
  callCount: number
  lastCalledAt?: number
  errors: Error[]
}

/**
 * 事件统计信息
 */
interface EventStats {
  publishCount: number
  listenerCount: number
  errorCount: number
  averageExecutionTime: number
  lastPublishedAt?: number
}

/**
 * 重构后的事件总线类
 * 实现观察者模式，支持类型安全的事件通信、优先级处理、性能监控
 */
export class EventBus {
  private listeners: Map<string, Map<EventCallback, ListenerInfo>> = new Map()
  private eventStats: Map<string, EventStats> = new Map()
  private debugMode: boolean = false
  private maxListeners: number = 100
  private performanceMonitoring: boolean = false

  constructor(options: {
    debugMode?: boolean
    maxListeners?: number
    performanceMonitoring?: boolean
  } = {}) {
    this.debugMode = options.debugMode ?? false
    this.maxListeners = options.maxListeners ?? 100
    this.performanceMonitoring = options.performanceMonitoring ?? false
  }

  /**
   * 订阅事件
   * @param event 事件类型
   * @param callback 回调函数
   * @param config 监听器配置
   */
  subscribe<K extends keyof ContentEventMap>(
    event: K,
    callback: EventCallback<ContentEventMap[K]>,
    config?: EventListenerConfig
  ): () => void
  subscribe(
    event: ContentEventType,
    callback: EventCallback,
    config?: EventListenerConfig
  ): () => void
  subscribe(
    event: ContentEventType,
    callback: EventCallback,
    config: EventListenerConfig = {}
  ): () => void {
    // 检查监听器数量限制
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Map())
      this.eventStats.set(event, {
        publishCount: 0,
        listenerCount: 0,
        errorCount: 0,
        averageExecutionTime: 0
      })
    }

    const eventListeners = this.listeners.get(event)!
    const stats = this.eventStats.get(event)!

    if (eventListeners.size >= this.maxListeners) {
      throw new Error(`[EventBus] Maximum listeners (${this.maxListeners}) exceeded for event: ${event}`)
    }

    // 创建监听器信息
    const listenerInfo: ListenerInfo = {
      callback,
      config,
      addedAt: Date.now(),
      callCount: 0,
      errors: []
    }

    eventListeners.set(callback, listenerInfo)
    stats.listenerCount = eventListeners.size

    if (this.debugMode) {
      console.log(`[EventBus] Subscribed to event: ${event}, listeners: ${stats.listenerCount}`)
    }

    // 返回取消订阅函数
    return () => this.unsubscribe(event, callback)
  }

  /**
   * 订阅事件（仅触发一次）
   * @param event 事件类型
   * @param callback 回调函数
   * @param config 监听器配置
   */
  once<K extends keyof ContentEventMap>(
    event: K,
    callback: EventCallback<ContentEventMap[K]>,
    config?: EventListenerConfig
  ): () => void
  once(
    event: ContentEventType,
    callback: EventCallback,
    config?: EventListenerConfig
  ): () => void
  once(
    event: ContentEventType,
    callback: EventCallback,
    config: EventListenerConfig = {}
  ): () => void {
    // 创建一次性回调包装器
    const onceWrapper: EventCallback = (eventData) => {
      try {
        callback(eventData)
      } finally {
        this.unsubscribe(event, onceWrapper)
      }
    }

    // 标记为一次性监听器
    const onceConfig = { ...config, once: true }
    return this.subscribe(event, onceWrapper, onceConfig)
  }

  /**
   * 取消订阅事件
   * @param event 事件类型
   * @param callback 回调函数
   */
  unsubscribe<K extends keyof ContentEventMap>(
    event: K,
    callback: EventCallback<ContentEventMap[K]>
  ): void
  unsubscribe(event: ContentEventType, callback: EventCallback): void
  unsubscribe(event: ContentEventType, callback: EventCallback): void {
    const eventListeners = this.listeners.get(event)
    const stats = this.eventStats.get(event)

    if (eventListeners && eventListeners.has(callback)) {
      eventListeners.delete(callback)

      if (stats) {
        stats.listenerCount = eventListeners.size
      }

      // 如果没有监听器了，清理事件
      if (eventListeners.size === 0) {
        this.listeners.delete(event)
        this.eventStats.delete(event)
      }

      if (this.debugMode) {
        console.log(`[EventBus] Unsubscribed from event: ${event}, remaining listeners: ${eventListeners.size}`)
      }
    }
  }

  /**
   * 发布事件
   * @param event 事件类型
   * @param data 事件数据
   * @param metadata 事件元数据
   */
  publish<K extends keyof ContentEventMap>(
    event: K,
    data: ContentEventMap[K],
    metadata?: Partial<EventMetadata>
  ): void
  publish(
    event: ContentEventType,
    data: any,
    metadata?: Partial<EventMetadata>
  ): void
  publish(
    event: ContentEventType,
    data: any,
    metadata: Partial<EventMetadata> = {}
  ): void {
    const startTime = this.performanceMonitoring ? performance.now() : 0
    const eventListeners = this.listeners.get(event)
    const stats = this.eventStats.get(event)

    if (!eventListeners || eventListeners.size === 0) {
      if (this.debugMode) {
        console.log(`[EventBus] No listeners for event: ${event}`)
      }
      return
    }

    // 创建完整的事件对象
    const fullEvent: ContentEvent = {
      type: event,
      data,
      metadata: {
        id: this.generateEventId(),
        timestamp: Date.now(),
        priority: metadata.priority ?? EventPriority.NORMAL,
        source: metadata.source,
        tags: metadata.tags,
        cancelable: metadata.cancelable ?? false,
        cancelled: false,
        ...metadata
      }
    }

    if (this.debugMode) {
      console.log(`[EventBus] Publishing event: ${event}`, fullEvent)
    }

    // 按优先级排序监听器
    const sortedListeners = Array.from(eventListeners.entries())
      .filter(([_, info]) => this.shouldExecuteListener(info, fullEvent))
      .sort(([_, a], [__, b]) => (b.config.minPriority ?? 0) - (a.config.minPriority ?? 0))

    let executionTime = 0
    let errorCount = 0

    // 执行监听器
    for (const [callback, listenerInfo] of sortedListeners) {
      if (fullEvent.metadata.cancelled) {
        break
      }

      try {
        const execStart = this.performanceMonitoring ? performance.now() : 0

        // 执行回调
        const result = callback(fullEvent)

        // 处理异步回调
        if (result instanceof Promise) {
          result.catch(error => {
            listenerInfo.errors.push(error)
            console.error(`[EventBus] Async error in listener for ${event}:`, error)
          })
        }

        if (this.performanceMonitoring) {
          const execTime = performance.now() - execStart
          executionTime += execTime
        }

        // 更新监听器统计
        listenerInfo.callCount++
        listenerInfo.lastCalledAt = Date.now()

        // 如果是一次性监听器，标记为删除
        if (listenerInfo.config.once) {
          eventListeners.delete(callback)
        }

      } catch (error) {
        errorCount++
        listenerInfo.errors.push(error as Error)
        console.error(`[EventBus] Error in listener for ${event}:`, error)
      }
    }

    // 更新事件统计
    if (stats) {
      stats.publishCount++
      stats.errorCount += errorCount
      stats.lastPublishedAt = Date.now()

      if (this.performanceMonitoring && sortedListeners.length > 0) {
        const avgTime = executionTime / sortedListeners.length
        stats.averageExecutionTime = (stats.averageExecutionTime + avgTime) / 2
      }
    }
  }

  /**
   * 生成唯一的事件ID
   */
  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 判断是否应该执行监听器
   */
  private shouldExecuteListener(listenerInfo: ListenerInfo, event: ContentEvent): boolean {
    const { config } = listenerInfo

    // 检查优先级过滤
    if (config.minPriority !== undefined && event.metadata.priority < config.minPriority) {
      return false
    }

    // 检查标签过滤
    if (config.tags && config.tags.length > 0) {
      const eventTags = event.metadata.tags || []
      const hasMatchingTag = config.tags.some(tag => eventTags.includes(tag))
      if (!hasMatchingTag) {
        return false
      }
    }

    // 检查超时
    if (config.timeout && (Date.now() - listenerInfo.addedAt) > config.timeout) {
      return false
    }

    return true
  }

  /**
   * 获取事件的订阅者数量
   * @param event 事件名称
   * @returns 订阅者数量
   */
  getListenerCount(event: string): number {
    const eventListeners = this.listeners.get(event)
    return eventListeners ? eventListeners.size : 0
  }

  /**
   * 获取所有事件名称
   * @returns 事件名称数组
   */
  getEventNames(): string[] {
    return Array.from(this.listeners.keys())
  }

  /**
   * 获取事件统计信息
   * @param event 事件名称
   * @returns 事件统计信息
   */
  getEventStats(event: string): EventStats | undefined {
    return this.eventStats.get(event)
  }

  /**
   * 获取所有事件统计信息
   * @returns 所有事件统计信息
   */
  getAllEventStats(): Map<string, EventStats> {
    return new Map(this.eventStats)
  }

  /**
   * 获取监听器信息
   * @param event 事件名称
   * @returns 监听器信息数组
   */
  getListenerInfo(event: string): ListenerInfo[] {
    const eventListeners = this.listeners.get(event)
    return eventListeners ? Array.from(eventListeners.values()) : []
  }

  /**
   * 清除所有事件监听器
   */
  clear(): void {
    this.listeners.clear()
    this.eventStats.clear()

    if (this.debugMode) {
      console.log('[EventBus] All listeners cleared')
    }
  }

  /**
   * 清除指定事件的所有监听器
   * @param event 事件名称
   */
  clearEvent(event: string): void {
    this.listeners.delete(event)
    this.eventStats.delete(event)

    if (this.debugMode) {
      console.log(`[EventBus] Cleared all listeners for event: ${event}`)
    }
  }

  /**
   * 启用/禁用调试模式
   * @param enabled 是否启用
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled
  }

  /**
   * 启用/禁用性能监控
   * @param enabled 是否启用
   */
  setPerformanceMonitoring(enabled: boolean): void {
    this.performanceMonitoring = enabled
  }

  /**
   * 设置最大监听器数量
   * @param max 最大数量
   */
  setMaxListeners(max: number): void {
    this.maxListeners = max
  }
}

// 导出单例实例
export const eventBus = new EventBus({
  debugMode: process.env.NODE_ENV === 'development',
  performanceMonitoring: process.env.NODE_ENV === 'development'
})
