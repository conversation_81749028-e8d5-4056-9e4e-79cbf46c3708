/**
 * 事件总线系统
 * 提供类型安全的事件订阅、发布、取消订阅功能
 */

// 事件类型定义
export interface EventMap {
  'input-focused': { element: HTMLElement; value: string }
  'input-changed': { element: HTMLElement; value: string }
  'input-blurred': { element: HTMLElement; value: string }
  'element-found': { type: string; element: HTMLElement }
  'element-lost': { type: string }
  'page-changed': { url: string }
  'archive-requested': { content: string; promptId: string }
  'archive-completed': { promptId: string; success: boolean }
}

export type EventCallback<T = any> = (data: T) => void

/**
 * 事件总线类
 * 实现观察者模式，支持类型安全的事件通信
 */
export class EventBus {
  private listeners: Map<string, Set<EventCallback>> = new Map()
  private onceListeners: Map<string, Set<EventCallback>> = new Map()
  private debugMode: boolean = false

  constructor(debugMode: boolean = false) {
    this.debugMode = debugMode
  }

  /**
   * 订阅事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  subscribe<K extends keyof EventMap>(
    event: K,
    callback: EventCallback<EventMap[K]>
  ): void
  subscribe(event: string, callback: EventCallback): void
  subscribe(event: string, callback: EventCallback): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    
    this.listeners.get(event)!.add(callback)
    
    if (this.debugMode) {
      console.log(`[EventBus] Subscribed to event: ${event}`)
    }
  }

  /**
   * 订阅事件（仅触发一次）
   * @param event 事件名称
   * @param callback 回调函数
   */
  once<K extends keyof EventMap>(
    event: K,
    callback: EventCallback<EventMap[K]>
  ): void
  once(event: string, callback: EventCallback): void
  once(event: string, callback: EventCallback): void {
    if (!this.onceListeners.has(event)) {
      this.onceListeners.set(event, new Set())
    }
    
    this.onceListeners.get(event)!.add(callback)
    
    if (this.debugMode) {
      console.log(`[EventBus] Subscribed once to event: ${event}`)
    }
  }

  /**
   * 取消订阅事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  unsubscribe<K extends keyof EventMap>(
    event: K,
    callback: EventCallback<EventMap[K]>
  ): void
  unsubscribe(event: string, callback: EventCallback): void
  unsubscribe(event: string, callback: EventCallback): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.delete(callback)
      if (listeners.size === 0) {
        this.listeners.delete(event)
      }
    }

    const onceListeners = this.onceListeners.get(event)
    if (onceListeners) {
      onceListeners.delete(callback)
      if (onceListeners.size === 0) {
        this.onceListeners.delete(event)
      }
    }
    
    if (this.debugMode) {
      console.log(`[EventBus] Unsubscribed from event: ${event}`)
    }
  }

  /**
   * 发布事件
   * @param event 事件名称
   * @param data 事件数据
   */
  publish<K extends keyof EventMap>(event: K, data: EventMap[K]): void
  publish(event: string, data: any): void
  publish(event: string, data: any): void {
    if (this.debugMode) {
      console.log(`[EventBus] Publishing event: ${event}`, data)
    }

    // 处理普通订阅者
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`[EventBus] Error in event listener for ${event}:`, error)
        }
      })
    }

    // 处理一次性订阅者
    const onceListeners = this.onceListeners.get(event)
    if (onceListeners) {
      onceListeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`[EventBus] Error in once listener for ${event}:`, error)
        }
      })
      // 清除一次性监听器
      this.onceListeners.delete(event)
    }
  }

  /**
   * 获取事件的订阅者数量
   * @param event 事件名称
   * @returns 订阅者数量
   */
  getListenerCount(event: string): number {
    const listeners = this.listeners.get(event)?.size || 0
    const onceListeners = this.onceListeners.get(event)?.size || 0
    return listeners + onceListeners
  }

  /**
   * 获取所有事件名称
   * @returns 事件名称数组
   */
  getEventNames(): string[] {
    const allEvents = new Set([
      ...this.listeners.keys(),
      ...this.onceListeners.keys()
    ])
    return Array.from(allEvents)
  }

  /**
   * 清除所有事件监听器
   */
  clear(): void {
    this.listeners.clear()
    this.onceListeners.clear()
    
    if (this.debugMode) {
      console.log('[EventBus] All listeners cleared')
    }
  }

  /**
   * 清除指定事件的所有监听器
   * @param event 事件名称
   */
  clearEvent(event: string): void {
    this.listeners.delete(event)
    this.onceListeners.delete(event)
    
    if (this.debugMode) {
      console.log(`[EventBus] Cleared all listeners for event: ${event}`)
    }
  }

  /**
   * 启用/禁用调试模式
   * @param enabled 是否启用
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled
  }
}

// 导出单例实例
export const eventBus = new EventBus(process.env.NODE_ENV === 'development')
