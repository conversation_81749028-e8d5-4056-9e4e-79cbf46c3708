import { ChatHistoryWithPlatform } from '../types/database_entity'
import { ChatHistoryWithPlatformAndPrompt } from '../lib/database/dexie'
import { PlatformIcon } from './PlatformIcon'
import { toast } from './Toast'

export interface HistoryBubbleOptions {
  maxItems?: number
  maxWidth?: number
  animationDuration?: number
  showPlatformIcons?: boolean
}

export class HistoryBubble {
  private container: HTMLElement | null = null
  private isVisible: boolean = false
  private chatHistory: ChatHistoryWithPlatformAndPrompt[] = []
  private options: Required<HistoryBubbleOptions>
  private platformIcon: PlatformIcon

  constructor(options: HistoryBubbleOptions = {}) {
    this.options = {
      maxItems: options.maxItems || 10,
      maxWidth: options.maxWidth || 300,
      animationDuration: options.animationDuration || 200,
      showPlatformIcons: options.showPlatformIcons !== false
    }
    
    this.platformIcon = new PlatformIcon()
    this.createContainer()
    this.addStyles()
  }

  /**
   * 创建气泡容器
   */
  private createContainer(): void {
    this.container = document.createElement('div')
    this.container.id = 'echosync-history-bubble'
    this.container.className = 'echosync-history-bubble'
    
    // 设置基础样式
    Object.assign(this.container.style, {
      position: 'fixed',
      zIndex: '10002',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '12px',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      maxWidth: `${this.options.maxWidth}px`,
      maxHeight: '400px',
      overflowY: 'auto',
      opacity: '0',
      transform: 'scale(0.9) translateY(10px)',
      transition: `all ${this.options.animationDuration}ms cubic-bezier(0.4, 0, 0.2, 1)`,
      pointerEvents: 'none',
      display: 'none'
    })

    document.body.appendChild(this.container)
  }

  /**
   * 添加样式
   */
  private addStyles(): void {
    if (document.getElementById('echosync-history-bubble-styles')) return

    const style = document.createElement('style')
    style.id = 'echosync-history-bubble-styles'
    style.textContent = `
      .echosync-history-bubble {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.4;
      }

      .echosync-history-bubble::-webkit-scrollbar {
        width: 6px;
      }

      .echosync-history-bubble::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
      }

      .echosync-history-bubble::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }

      .echosync-history-bubble::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
      }

      .echosync-history-item {
        padding: 12px 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        cursor: pointer;
        transition: background-color 0.15s ease;
        position: relative;
      }

      .echosync-history-item:last-child {
        border-bottom: none;
      }

      .echosync-history-item:hover {
        background-color: rgba(139, 92, 246, 0.05);
      }

      .echosync-history-item.clicked {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        transform: scale(1.02);
        transition: all 0.3s ease;
      }

      .echosync-history-item.clicked .echosync-history-item-prompt {
        color: white !important;
      }

      .echosync-history-item.clicked .echosync-history-item-meta {
        color: rgba(255, 255, 255, 0.8) !important;
      }

      .echosync-history-item-content {
        display: flex;
        align-items: flex-start;
        gap: 8px;
      }

      .echosync-history-item-text {
        flex: 1;
        min-width: 0;
      }

      .echosync-history-item-prompt {
        color: #374151;
        font-weight: 500;
        margin-bottom: 4px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-word;
      }

      .echosync-history-item-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #6b7280;
      }

      .echosync-history-item-time {
        white-space: nowrap;
      }

      .echosync-history-item-platforms {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-left: auto;
      }

      .echosync-history-empty {
        padding: 24px 16px;
        text-align: center;
        color: #6b7280;
        font-size: 13px;
      }

      .echosync-history-header {
        padding: 12px 16px 8px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        background: rgba(139, 92, 246, 0.02);
      }

      .echosync-history-title {
        font-size: 13px;
        font-weight: 600;
        color: #8b5cf6;
        margin: 0;
      }

      @keyframes echosync-bubble-show {
        from {
          opacity: 0;
          transform: scale(0.9) translateY(10px);
        }
        to {
          opacity: 1;
          transform: scale(1) translateY(0);
        }
      }

      @keyframes echosync-bubble-hide {
        from {
          opacity: 1;
          transform: scale(1) translateY(0);
        }
        to {
          opacity: 0;
          transform: scale(0.9) translateY(10px);
        }
      }
    `
    document.head.appendChild(style)
  }

  /**
   * 更新聊天历史数据
   */
  public updateHistory(history: ChatHistoryWithPlatformAndPrompt[]): void {
    this.chatHistory = history.slice(0, this.options.maxItems)
    this.renderContent()
  }

  /**
   * 渲染内容
   */
  private renderContent(): void {
    if (!this.container) return

    // 清空容器
    this.container.innerHTML = ''

    // 添加标题
    const header = document.createElement('div')
    header.className = 'echosync-history-header'
    header.innerHTML = `
      <h3 class="echosync-history-title">最近提示词</h3>
    `
    this.container.appendChild(header)

    if (this.chatHistory.length === 0) {
      // 显示空状态
      const emptyDiv = document.createElement('div')
      emptyDiv.className = 'echosync-history-empty'
      emptyDiv.textContent = '暂无历史提示词'
      this.container.appendChild(emptyDiv)
      return
    }

    // 渲染历史记录
    this.chatHistory.forEach((chat, index) => {
      const item = this.createHistoryItem(chat, index)
      this.container!.appendChild(item)
    })
  }

  /**
   * 创建历史记录项
   */
  private createHistoryItem(chat: ChatHistoryWithPlatformAndPrompt, index: number): HTMLElement {
    const item = document.createElement('div')
    item.className = 'echosync-history-item'
    item.dataset.chatId = chat.id.toString()
    item.dataset.chatUid = chat.chat_uid

    // 格式化时间
    const timeStr = this.formatTime(chat.create_time)

    // 创建内容
    const content = document.createElement('div')
    content.className = 'echosync-history-item-content'

    const textDiv = document.createElement('div')
    textDiv.className = 'echosync-history-item-text'

    const promptDiv = document.createElement('div')
    promptDiv.className = 'echosync-history-item-prompt'
    promptDiv.textContent = chat.chat_prompt
    promptDiv.title = chat.chat_prompt

    const metaDiv = document.createElement('div')
    metaDiv.className = 'echosync-history-item-meta'

    const timeSpan = document.createElement('span')
    timeSpan.className = 'echosync-history-item-time'
    timeSpan.textContent = timeStr

    const platformsDiv = document.createElement('div')
    platformsDiv.className = 'echosync-history-item-platforms'

    // 添加平台图标
    if (this.options.showPlatformIcons && (chat.platform_icon || chat.platform_icon_base64)) {
      // console.log(`【HistoryBubble】为${chat.platform_name}创建图标:`, {
      //   hasIcon: !!chat.platform_icon,
      //   hasIconBase64: !!chat.platform_icon_base64,
      //   base64Length: chat.platform_icon_base64?.length || 0
      // })

      const iconElement = this.platformIcon.createIcon({
        id: chat.platform_id,
        name: chat.platform_name,
        url: chat.platform_url,
        icon: chat.platform_icon,
        icon_base64: chat.platform_icon_base64, // 使用icon_base64字段
        is_delete: 0
      }, { size: 16 })
      platformsDiv.appendChild(iconElement)
    }

    metaDiv.appendChild(timeSpan)
    metaDiv.appendChild(platformsDiv)

    textDiv.appendChild(promptDiv)
    textDiv.appendChild(metaDiv)
    content.appendChild(textDiv)
    item.appendChild(content)

    // 添加点击事件
    item.addEventListener('click', (event) => {
      this.handleItemClick(chat, event.currentTarget as HTMLElement)
    })

    return item
  }

  /**
   * 处理项目点击
   */
  private handleItemClick(chat: ChatHistoryWithPlatformAndPrompt, clickedElement: HTMLElement): void {
    // 添加高亮样式
    clickedElement.classList.add('clicked')

    // 显示Toast提示
    toast.success('提示词已复制', { duration: 2000 })

    // 300ms后移除高亮效果
    setTimeout(() => {
      clickedElement.classList.remove('clicked')
    }, 300)

    // 触发自定义事件
    const event = new CustomEvent('echosync:history-item-click', {
      detail: { chat }
    })
    document.dispatchEvent(event)

    // 延迟隐藏气泡，让用户看到高亮效果
    setTimeout(() => {
      this.hide()
    }, 200)
  }

  /**
   * 格式化时间
   */
  private formatTime(timestamp: number): string {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    
    return new Date(timestamp).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    })
  }

  /**
   * 显示气泡
   */
  public show(anchorElement: HTMLElement): void {
    if (!this.container || this.isVisible) return

    // 计算位置
    const rect = anchorElement.getBoundingClientRect()
    const bubbleHeight = Math.min(400, this.chatHistory.length * 60 + 60)
    
    // 确定显示位置（气泡上方）
    let top = rect.top - bubbleHeight - 10
    let left = rect.left + (rect.width / 2) - (this.options.maxWidth / 2)

    // 边界检查
    if (top < 10) {
      top = rect.bottom + 10
    }
    if (left < 10) {
      left = 10
    }
    if (left + this.options.maxWidth > window.innerWidth - 10) {
      left = window.innerWidth - this.options.maxWidth - 10
    }

    // 设置位置
    this.container.style.top = `${top}px`
    this.container.style.left = `${left}px`
    this.container.style.display = 'block'
    this.container.style.pointerEvents = 'auto'

    // 触发显示动画
    requestAnimationFrame(() => {
      if (this.container) {
        this.container.style.opacity = '1'
        this.container.style.transform = 'scale(1) translateY(0)'
      }
    })

    this.isVisible = true
  }

  /**
   * 隐藏气泡
   */
  public hide(): void {
    if (!this.container || !this.isVisible) return

    this.container.style.opacity = '0'
    this.container.style.transform = 'scale(0.9) translateY(10px)'
    this.container.style.pointerEvents = 'none'

    setTimeout(() => {
      if (this.container) {
        this.container.style.display = 'none'
      }
    }, this.options.animationDuration)

    this.isVisible = false
  }

  /**
   * 切换显示状态
   */
  public toggle(anchorElement: HTMLElement): void {
    if (this.isVisible) {
      this.hide()
    } else {
      this.show(anchorElement)
    }
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    if (this.container) {
      this.container.remove()
      this.container = null
    }
    
    const styles = document.getElementById('echosync-history-bubble-styles')
    if (styles) {
      styles.remove()
    }
    
    this.platformIcon.destroy()
    this.isVisible = false
  }

  /**
   * 获取可见状态
   */
  public get visible(): boolean {
    return this.isVisible
  }
}
