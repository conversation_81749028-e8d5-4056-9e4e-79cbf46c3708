/**
 * ElementFinder 单元测试
 */

import { ElementFinder } from '../ElementFinder'
import { DOMUtils } from '../DOMUtils'

// Mock DOMUtils
jest.mock('../DOMUtils')

describe('ElementFinder', () => {
  let elementFinder: ElementFinder
  let mockDOMUtils: jest.Mocked<DOMUtils>

  beforeEach(() => {
    // 清理DOM
    document.body.innerHTML = ''
    
    // 创建ElementFinder实例
    elementFinder = new ElementFinder()
    
    // 获取mock的DOMUtils实例
    mockDOMUtils = DOMUtils.getInstance() as jest.Mocked<DOMUtils>
    
    // 清理缓存
    elementFinder.clearCache()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('基本查找功能', () => {
    test('应该能够找到存在的元素', async () => {
      // 创建测试元素
      const testElement = document.createElement('div')
      testElement.id = 'test-element'
      testElement.className = 'test-class'
      document.body.appendChild(testElement)

      // Mock isElementVisible返回true
      mockDOMUtils.isElementVisible.mockReturnValue(true)

      const result = await elementFinder.findElement(['#test-element'])

      expect(result.element).toBe(testElement)
      expect(result.selector).toBe('#test-element')
      expect(result.selectorIndex).toBe(0)
      expect(result.fromCache).toBe(false)
      expect(result.findTime).toBeGreaterThanOrEqual(0)
    })

    test('应该按优先级尝试多个选择器', async () => {
      // 创建测试元素
      const testElement = document.createElement('div')
      testElement.className = 'second-class'
      document.body.appendChild(testElement)

      // Mock isElementVisible返回true
      mockDOMUtils.isElementVisible.mockReturnValue(true)

      const result = await elementFinder.findElement([
        '#non-existent',
        '.second-class',
        '.third-class'
      ])

      expect(result.element).toBe(testElement)
      expect(result.selector).toBe('.second-class')
      expect(result.selectorIndex).toBe(1)
    })

    test('应该返回null当没有找到元素时', async () => {
      const result = await elementFinder.findElement(['#non-existent'])

      expect(result.element).toBeNull()
      expect(result.selector).toBe('#non-existent')
      expect(result.selectorIndex).toBe(-1)
    })

    test('应该忽略不可见的元素', async () => {
      // 创建不可见的测试元素
      const hiddenElement = document.createElement('div')
      hiddenElement.id = 'hidden-element'
      hiddenElement.style.display = 'none'
      document.body.appendChild(hiddenElement)

      // Mock isElementVisible返回false
      mockDOMUtils.isElementVisible.mockReturnValue(false)

      const result = await elementFinder.findElement(['#hidden-element'])

      expect(result.element).toBeNull()
    })
  })

  describe('缓存机制', () => {
    test('应该缓存查找结果', async () => {
      // 创建测试元素
      const testElement = document.createElement('div')
      testElement.id = 'cached-element'
      document.body.appendChild(testElement)

      // Mock isElementVisible返回true
      mockDOMUtils.isElementVisible.mockReturnValue(true)

      // 第一次查找
      const result1 = await elementFinder.findElement(['#cached-element'])
      expect(result1.fromCache).toBe(false)

      // 第二次查找应该从缓存获取
      const result2 = await elementFinder.findElement(['#cached-element'])
      expect(result2.fromCache).toBe(true)
      expect(result2.element).toBe(testElement)
    })

    test('应该正确计算缓存统计', async () => {
      // 创建测试元素
      const testElement = document.createElement('div')
      testElement.id = 'stats-element'
      document.body.appendChild(testElement)

      // Mock isElementVisible返回true
      mockDOMUtils.isElementVisible.mockReturnValue(true)

      // 初始统计
      let stats = elementFinder.getCacheStats()
      expect(stats.size).toBe(0)
      expect(stats.hitRate).toBe(0)
      expect(stats.missRate).toBe(0)

      // 第一次查找（缓存未命中）
      await elementFinder.findElement(['#stats-element'])
      stats = elementFinder.getCacheStats()
      expect(stats.size).toBe(1)
      expect(stats.hitRate).toBe(0)
      expect(stats.missRate).toBe(100)

      // 第二次查找（缓存命中）
      await elementFinder.findElement(['#stats-element'])
      stats = elementFinder.getCacheStats()
      expect(stats.hitRate).toBe(50)
      expect(stats.missRate).toBe(50)
    })

    test('应该能够清除缓存', async () => {
      // 创建测试元素
      const testElement = document.createElement('div')
      testElement.id = 'clear-cache-element'
      document.body.appendChild(testElement)

      // Mock isElementVisible返回true
      mockDOMUtils.isElementVisible.mockReturnValue(true)

      // 查找元素以填充缓存
      await elementFinder.findElement(['#clear-cache-element'])
      
      let stats = elementFinder.getCacheStats()
      expect(stats.size).toBe(1)

      // 清除缓存
      elementFinder.clearCache()
      
      stats = elementFinder.getCacheStats()
      expect(stats.size).toBe(0)
      expect(stats.hitRate).toBe(0)
      expect(stats.missRate).toBe(0)
    })

    test('应该验证缓存元素的有效性', async () => {
      // 创建测试元素
      const testElement = document.createElement('div')
      testElement.id = 'validity-element'
      document.body.appendChild(testElement)

      // Mock isElementVisible返回true
      mockDOMUtils.isElementVisible.mockReturnValue(true)

      // 第一次查找
      await elementFinder.findElement(['#validity-element'])

      // 移除元素
      document.body.removeChild(testElement)

      // 第二次查找应该重新查找，因为缓存的元素已失效
      const result = await elementFinder.findElement(['#validity-element'])
      expect(result.element).toBeNull()
    })
  })

  describe('同步查找', () => {
    test('应该能够同步查找元素', () => {
      // 创建测试元素
      const testElement = document.createElement('div')
      testElement.id = 'sync-element'
      document.body.appendChild(testElement)

      // Mock isElementVisible返回true
      mockDOMUtils.isElementVisible.mockReturnValue(true)

      const result = elementFinder.findElementSync(['#sync-element'])

      expect(result).toBe(testElement)
    })

    test('同步查找应该返回null当元素不存在时', () => {
      const result = elementFinder.findElementSync(['#non-existent-sync'])

      expect(result).toBeNull()
    })
  })

  describe('多元素查找', () => {
    test('应该能够查找多个元素', async () => {
      // 创建多个测试元素
      const element1 = document.createElement('div')
      element1.className = 'multi-element'
      const element2 = document.createElement('div')
      element2.className = 'multi-element'
      const element3 = document.createElement('span')
      element3.className = 'multi-element'

      document.body.appendChild(element1)
      document.body.appendChild(element2)
      document.body.appendChild(element3)

      const results = await elementFinder.findElements(['.multi-element'])

      expect(results).toHaveLength(3)
      expect(results).toContain(element1)
      expect(results).toContain(element2)
      expect(results).toContain(element3)
    })

    test('应该处理无效选择器', async () => {
      const results = await elementFinder.findElements(['[invalid selector'])

      expect(results).toHaveLength(0)
    })
  })

  describe('等待查找功能', () => {
    test('应该能够等待元素出现', async () => {
      // Mock waitForElement
      const testElement = document.createElement('div')
      testElement.id = 'wait-element'
      
      mockDOMUtils.waitForElement.mockResolvedValue(testElement)
      mockDOMUtils.isElementVisible.mockReturnValue(true)

      const result = await elementFinder.findElement(['#wait-element'], {
        timeout: 1000
      })

      expect(result.element).toBe(testElement)
      expect(mockDOMUtils.waitForElement).toHaveBeenCalledWith('#wait-element', 1000)
    })

    test('应该处理等待超时', async () => {
      // Mock waitForElement返回null（超时）
      mockDOMUtils.waitForElement.mockResolvedValue(null)

      const result = await elementFinder.findElement(['#timeout-element'], {
        timeout: 100
      })

      expect(result.element).toBeNull()
    })
  })

  describe('重试机制', () => {
    test('应该在失败时重试', async () => {
      // 创建测试元素
      const testElement = document.createElement('div')
      testElement.id = 'retry-element'

      // Mock isElementVisible前两次返回false，第三次返回true
      mockDOMUtils.isElementVisible
        .mockReturnValueOnce(false)
        .mockReturnValueOnce(false)
        .mockReturnValue(true)

      // 延迟添加元素到DOM
      setTimeout(() => {
        document.body.appendChild(testElement)
      }, 100)

      const result = await elementFinder.findElement(['#retry-element'], {
        retryCount: 3,
        retryInterval: 50
      })

      expect(result.element).toBe(testElement)
    })
  })

  describe('错误处理', () => {
    test('应该处理无效选择器', async () => {
      const result = await elementFinder.findElement(['[invalid selector'])

      expect(result.element).toBeNull()
    })

    test('应该在调试模式下记录警告', async () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()

      await elementFinder.findElement(['[invalid selector'], {
        debug: true,
        retryCount: 1
      })

      expect(consoleSpy).toHaveBeenCalled()
      consoleSpy.mockRestore()
    })
  })
})
