/**
 * Answer存储功能性能测试脚本
 * 测试大量数据下的性能表现
 */

class PerformanceTest {
  constructor() {
    this.results = []
  }

  /**
   * 运行性能测试
   */
  async runPerformanceTests() {
    console.group('【EchoSync】性能测试开始')
    
    try {
      await this.testPromptCachePerformance()
      await this.testBatchInsertPerformance()
      await this.testLargeDataQueryPerformance()
      await this.testMemoryUsage()
      
      this.printPerformanceResults()
    } catch (error) {
      console.error('性能测试失败:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 测试提示词缓存性能
   */
  async testPromptCachePerformance() {
    console.log('⚡ 性能测试1: 提示词缓存性能')
    
    const testPrompts = []
    for (let i = 0; i < 100; i++) {
      testPrompts.push(`测试提示词${i} - ${Date.now()}`)
    }

    // 第一次查询（无缓存）
    const startTime1 = performance.now()
    for (const prompt of testPrompts.slice(0, 10)) {
      await chatPromptService.findByPrompt(prompt)
    }
    const uncachedTime = performance.now() - startTime1

    // 第二次查询（有缓存）
    const startTime2 = performance.now()
    for (const prompt of testPrompts.slice(0, 10)) {
      await chatPromptService.findByPrompt(prompt)
    }
    const cachedTime = performance.now() - startTime2

    this.addPerformanceResult('提示词缓存', {
      uncachedTime: uncachedTime.toFixed(2) + 'ms',
      cachedTime: cachedTime.toFixed(2) + 'ms',
      improvement: ((uncachedTime - cachedTime) / uncachedTime * 100).toFixed(1) + '%'
    })
  }

  /**
   * 测试批量插入性能
   */
  async testBatchInsertPerformance() {
    console.log('⚡ 性能测试2: 批量插入性能')
    
    const batchSize = 50
    const answers = []
    
    for (let i = 0; i < batchSize; i++) {
      answers.push({
        chat_answer: `测试答案${i} - ${Date.now()}`,
        chat_uid: `test-uid-${i}-${Date.now()}`,
        platform_id: 1
      })
    }

    // 测试批量插入
    const startTime = performance.now()
    const result = await chatHistoryService.createAnswerBatch(answers)
    const batchTime = performance.now() - startTime

    // 测试单个插入（对比）
    const singleStartTime = performance.now()
    for (let i = 0; i < 10; i++) {
      await chatHistoryService.create({
        chat_answer: `单个测试答案${i}`,
        chat_uid: `single-uid-${i}-${Date.now()}`,
        platform_id: 1
      })
    }
    const singleTime = performance.now() - singleStartTime

    this.addPerformanceResult('批量插入', {
      batchSize,
      batchTime: batchTime.toFixed(2) + 'ms',
      singleTime: singleTime.toFixed(2) + 'ms',
      efficiency: (singleTime / batchTime * batchSize / 10).toFixed(1) + 'x faster'
    })
  }

  /**
   * 测试大数据量查询性能
   */
  async testLargeDataQueryPerformance() {
    console.log('⚡ 性能测试3: 大数据量查询性能')
    
    // 测试获取历史记录
    const startTime1 = performance.now()
    const historyResult = await chatHistoryDatabaseProxy.getList({ limit: 100 })
    const historyTime = performance.now() - startTime1

    // 测试获取唯一聊天记录
    const startTime2 = performance.now()
    const uniqueResult = await chatHistoryDatabaseProxy.getUniqueChats({ limit: 50 })
    const uniqueTime = performance.now() - startTime2

    // 测试搜索功能
    const startTime3 = performance.now()
    const searchResult = await chatHistoryDatabaseProxy.search('测试', { limit: 20 })
    const searchTime = performance.now() - startTime3

    this.addPerformanceResult('大数据查询', {
      historyQuery: historyTime.toFixed(2) + 'ms',
      uniqueQuery: uniqueTime.toFixed(2) + 'ms',
      searchQuery: searchTime.toFixed(2) + 'ms',
      totalRecords: (historyResult.success ? historyResult.data.total : 0)
    })
  }

  /**
   * 测试内存使用情况
   */
  async testMemoryUsage() {
    console.log('⚡ 性能测试4: 内存使用情况')
    
    if (performance.memory) {
      const memoryBefore = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      }

      // 执行一些操作
      const operations = []
      for (let i = 0; i < 100; i++) {
        operations.push(chatPromptService.findByPrompt(`内存测试${i}`))
      }
      await Promise.all(operations)

      const memoryAfter = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      }

      this.addPerformanceResult('内存使用', {
        beforeUsed: (memoryBefore.used / 1024 / 1024).toFixed(2) + 'MB',
        afterUsed: (memoryAfter.used / 1024 / 1024).toFixed(2) + 'MB',
        difference: ((memoryAfter.used - memoryBefore.used) / 1024 / 1024).toFixed(2) + 'MB',
        totalAvailable: (memoryAfter.limit / 1024 / 1024).toFixed(0) + 'MB'
      })
    } else {
      this.addPerformanceResult('内存使用', {
        error: '浏览器不支持 performance.memory API'
      })
    }
  }

  /**
   * 添加性能测试结果
   */
  addPerformanceResult(testName, metrics) {
    this.results.push({
      test: testName,
      metrics,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 打印性能测试结果
   */
  printPerformanceResults() {
    console.group('📈 性能测试结果')
    
    this.results.forEach(result => {
      console.group(`🔍 ${result.test}`)
      console.table(result.metrics)
      console.groupEnd()
    })
    
    console.groupEnd()
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      results: this.results,
      recommendations: this.generateRecommendations()
    }
    
    console.log('📋 性能报告:', report)
    return report
  }

  /**
   * 生成性能优化建议
   */
  generateRecommendations() {
    const recommendations = []
    
    // 基于测试结果生成建议
    const cacheResult = this.results.find(r => r.test === '提示词缓存')
    if (cacheResult && parseFloat(cacheResult.metrics.improvement) < 50) {
      recommendations.push('考虑增加缓存TTL时间或优化缓存策略')
    }
    
    const memoryResult = this.results.find(r => r.test === '内存使用')
    if (memoryResult && parseFloat(memoryResult.metrics.difference) > 10) {
      recommendations.push('内存使用量较高，考虑优化数据结构或添加垃圾回收')
    }
    
    if (recommendations.length === 0) {
      recommendations.push('性能表现良好，无需特别优化')
    }
    
    return recommendations
  }
}

// 导出性能测试类
window.PerformanceTest = PerformanceTest

// 使用说明
console.log(`
【EchoSync】性能测试脚本已加载

使用方法:
const perfTest = new PerformanceTest()
perfTest.runPerformanceTests()

生成报告:
const report = perfTest.generateReport()
`)
