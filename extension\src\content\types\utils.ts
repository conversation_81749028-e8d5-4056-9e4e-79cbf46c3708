/**
 * 工具类相关类型定义
 * 统一管理所有工具类和辅助函数的类型和接口
 */

/**
 * DOM工具类接口
 */
export interface IDOMUtils {
  /** 等待页面加载完成 */
  waitForPageLoad(timeout?: number): Promise<void>
  /** 等待元素出现 */
  waitForElement(selector: string, timeout?: number): Promise<HTMLElement | null>
  /** 等待元素消失 */
  waitForElementToDisappear(selector: string, timeout?: number): Promise<void>
  /** 检查元素是否可见 */
  isElementVisible(element: HTMLElement): boolean
  /** 获取元素的绝对位置 */
  getElementPosition(element: HTMLElement): DOMRect
  /** 检查元素是否在视口内 */
  isElementInViewport(element: HTMLElement): boolean
  /** 滚动到元素位置 */
  scrollToElement(element: HTMLElement, behavior?: ScrollBehavior): void
  /** 创建元素 */
  createElement<K extends keyof HTMLElementTagNameMap>(
    tagName: K,
    attributes?: Record<string, string>,
    styles?: Record<string, string>
  ): HTMLElementTagNameMap[K]
  /** 安全地移除元素 */
  safeRemoveElement(element: HTMLElement): void
  /** 克隆元素 */
  cloneElement(element: HTMLElement, deep?: boolean): HTMLElement
}

/**
 * 元素查找器配置
 */
export interface ElementFinderConfig {
  /** 查找超时时间（毫秒） */
  timeout?: number
  /** 是否启用缓存 */
  enableCache?: boolean
  /** 缓存过期时间（毫秒） */
  cacheExpiry?: number
  /** 是否启用调试 */
  debug?: boolean
  /** 重试次数 */
  retryCount?: number
  /** 重试间隔（毫秒） */
  retryInterval?: number
}

/**
 * 元素查找结果
 */
export interface ElementFindResult {
  /** 找到的元素 */
  element: HTMLElement | null
  /** 使用的选择器 */
  selector: string
  /** 选择器索引 */
  selectorIndex: number
  /** 查找耗时（毫秒） */
  findTime: number
  /** 是否来自缓存 */
  fromCache: boolean
}

/**
 * 元素查找器接口
 */
export interface IElementFinder {
  /** 查找单个元素 */
  findElement(selectors: string[], config?: ElementFinderConfig): Promise<ElementFindResult>
  /** 查找多个元素 */
  findElements(selectors: string[], config?: ElementFinderConfig): Promise<HTMLElement[]>
  /** 查找元素（同步） */
  findElementSync(selectors: string[]): HTMLElement | null
  /** 清除缓存 */
  clearCache(): void
  /** 获取缓存统计 */
  getCacheStats(): {
    size: number
    hitRate: number
    missRate: number
  }
}

/**
 * 性能优化器配置
 */
export interface PerformanceOptimizerConfig {
  /** 是否启用防抖 */
  enableDebounce?: boolean
  /** 防抖延迟（毫秒） */
  debounceDelay?: number
  /** 是否启用节流 */
  enableThrottle?: boolean
  /** 节流间隔（毫秒） */
  throttleInterval?: number
  /** 是否启用内存监控 */
  enableMemoryMonitoring?: boolean
  /** 内存监控间隔（毫秒） */
  memoryMonitoringInterval?: number
  /** 最大内存使用量（字节） */
  maxMemoryUsage?: number
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  /** CPU使用率（百分比） */
  cpuUsage: number
  /** 内存使用量（字节） */
  memoryUsage: number
  /** 堆内存使用量（字节） */
  heapUsage: number
  /** 事件循环延迟（毫秒） */
  eventLoopDelay: number
  /** 函数调用次数 */
  functionCallCount: number
  /** 平均执行时间（毫秒） */
  averageExecutionTime: number
  /** 错误计数 */
  errorCount: number
  /** 时间戳 */
  timestamp: number
}

/**
 * 性能优化器接口
 */
export interface IPerformanceOptimizer {
  /** 防抖函数 */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    delay?: number
  ): (...args: Parameters<T>) => void
  /** 节流函数 */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    interval?: number
  ): (...args: Parameters<T>) => void
  /** 测量函数执行时间 */
  measureExecutionTime<T extends (...args: any[]) => any>(
    func: T,
    name?: string
  ): (...args: Parameters<T>) => ReturnType<T>
  /** 获取性能指标 */
  getMetrics(): PerformanceMetrics
  /** 开始性能监控 */
  startMonitoring(): void
  /** 停止性能监控 */
  stopMonitoring(): void
  /** 重置指标 */
  resetMetrics(): void
}

/**
 * 存储管理器配置
 */
export interface StorageManagerConfig {
  /** 存储类型 */
  storageType?: 'localStorage' | 'sessionStorage' | 'indexedDB'
  /** 存储前缀 */
  prefix?: string
  /** 是否启用加密 */
  enableEncryption?: boolean
  /** 加密密钥 */
  encryptionKey?: string
  /** 是否启用压缩 */
  enableCompression?: boolean
  /** 最大存储大小（字节） */
  maxStorageSize?: number
  /** 过期时间（毫秒） */
  defaultExpiry?: number
}

/**
 * 存储项接口
 */
export interface StorageItem<T = any> {
  /** 存储的值 */
  value: T
  /** 创建时间戳 */
  createdAt: number
  /** 过期时间戳 */
  expiresAt?: number
  /** 版本号 */
  version?: string
  /** 元数据 */
  metadata?: Record<string, any>
}

/**
 * 存储管理器接口
 */
export interface IStorageManager {
  /** 设置存储项 */
  setItem<T>(key: string, value: T, expiry?: number): Promise<void>
  /** 获取存储项 */
  getItem<T>(key: string): Promise<T | null>
  /** 删除存储项 */
  removeItem(key: string): Promise<void>
  /** 清除所有存储项 */
  clear(): Promise<void>
  /** 获取所有键 */
  getAllKeys(): Promise<string[]>
  /** 检查键是否存在 */
  hasItem(key: string): Promise<boolean>
  /** 获取存储大小 */
  getStorageSize(): Promise<number>
  /** 获取存储统计 */
  getStorageStats(): Promise<{
    totalItems: number
    totalSize: number
    expiredItems: number
  }>
}

/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  /** 日志级别 */
  level: LogLevel
  /** 日志消息 */
  message: string
  /** 时间戳 */
  timestamp: number
  /** 日志来源 */
  source?: string
  /** 上下文数据 */
  context?: any
  /** 错误信息 */
  error?: Error
  /** 堆栈跟踪 */
  stack?: string
}

/**
 * 日志器配置
 */
export interface LoggerConfig {
  /** 最小日志级别 */
  minLevel?: LogLevel
  /** 是否启用控制台输出 */
  enableConsole?: boolean
  /** 是否启用存储 */
  enableStorage?: boolean
  /** 最大日志条目数 */
  maxLogEntries?: number
  /** 日志格式化器 */
  formatter?: (entry: LogEntry) => string
  /** 日志过滤器 */
  filter?: (entry: LogEntry) => boolean
}

/**
 * 日志器接口
 */
export interface ILogger {
  /** 调试日志 */
  debug(message: string, context?: any): void
  /** 信息日志 */
  info(message: string, context?: any): void
  /** 警告日志 */
  warn(message: string, context?: any): void
  /** 错误日志 */
  error(message: string, error?: Error, context?: any): void
  /** 致命错误日志 */
  fatal(message: string, error?: Error, context?: any): void
  /** 获取所有日志 */
  getAllLogs(): LogEntry[]
  /** 清除日志 */
  clearLogs(): void
  /** 设置日志级别 */
  setLevel(level: LogLevel): void
  /** 获取日志级别 */
  getLevel(): LogLevel
}

/**
 * 错误处理器配置
 */
export interface ErrorHandlerConfig {
  /** 是否启用全局错误捕获 */
  enableGlobalCapture?: boolean
  /** 是否启用Promise错误捕获 */
  enablePromiseCapture?: boolean
  /** 错误报告回调 */
  onError?: (error: Error, context?: any) => void
  /** 最大错误数量 */
  maxErrors?: number
  /** 错误过滤器 */
  errorFilter?: (error: Error) => boolean
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  /** 错误对象 */
  error: Error
  /** 错误时间戳 */
  timestamp: number
  /** 错误来源 */
  source?: string
  /** 错误上下文 */
  context?: any
  /** 用户代理 */
  userAgent?: string
  /** 页面URL */
  url?: string
  /** 堆栈跟踪 */
  stack?: string
}

/**
 * 错误处理器接口
 */
export interface IErrorHandler {
  /** 处理错误 */
  handleError(error: Error, context?: any): void
  /** 获取所有错误 */
  getAllErrors(): ErrorInfo[]
  /** 清除错误 */
  clearErrors(): void
  /** 获取错误统计 */
  getErrorStats(): {
    totalErrors: number
    errorsByType: Record<string, number>
    recentErrors: ErrorInfo[]
  }
}

/**
 * 工具函数类型
 */
export type UtilityFunction<T extends any[], R> = (...args: T) => R
export type AsyncUtilityFunction<T extends any[], R> = (...args: T) => Promise<R>

/**
 * 通用回调函数类型
 */
export type Callback<T = void> = (result: T) => void
export type ErrorCallback = (error: Error) => void
export type SuccessCallback<T = void> = (result: T) => void

/**
 * 通用配置接口
 */
export interface BaseConfig {
  /** 是否启用 */
  enabled?: boolean
  /** 调试模式 */
  debug?: boolean
  /** 超时时间（毫秒） */
  timeout?: number
  /** 重试次数 */
  retryCount?: number
  /** 重试间隔（毫秒） */
  retryInterval?: number
}

/**
 * 通用结果接口
 */
export interface Result<T = any, E = Error> {
  /** 是否成功 */
  success: boolean
  /** 结果数据 */
  data?: T
  /** 错误信息 */
  error?: E
  /** 消息 */
  message?: string
  /** 时间戳 */
  timestamp: number
}

/**
 * 分页配置接口
 */
export interface PaginationConfig {
  /** 当前页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 总数量 */
  total?: number
  /** 总页数 */
  totalPages?: number
}

/**
 * 分页结果接口
 */
export interface PaginatedResult<T> {
  /** 数据列表 */
  items: T[]
  /** 分页信息 */
  pagination: PaginationConfig
  /** 是否有下一页 */
  hasNext: boolean
  /** 是否有上一页 */
  hasPrev: boolean
}
