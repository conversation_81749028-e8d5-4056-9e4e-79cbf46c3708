import { AIPlatform } from '@/types'

/**
 * 平台配置接口
 * 定义每个AI平台的基本信息和选择器配置
 */
export interface PlatformConfig {
  /** 平台基本信息 */
  name: string
  id: AIPlatform
  url: string
  
  /** 平台识别模式 */
  patterns: {
    /** 主机名匹配正则 */
    hostname: RegExp
    /** 有效路径匹配正则（可选） */
    validPath?: RegExp
  }
  
  /** DOM选择器配置 */
  selectors: {
    /** 输入框选择器列表（按优先级排序） */
    inputField: string[]
    /** 发送按钮选择器列表 */
    sendButton: string[]
    /** 消息容器选择器列表 */
    messageContainer: string[]
    /** 用户消息选择器（可选） */
    userMessage?: string[]
    /** AI回答消息选择器（可选） */
    assistantMessage?: string[]
    /** 对话标题选择器（可选） */
    conversationTitle?: string[]
  }
  
  /** 正则表达式模式配置 */
  regexPatterns?: {
    /** 消息内容提取正则 */
    messageExtraction?: RegExp
    /** 标题提取正则 */
    titleExtraction?: RegExp
    /** 内容清理正则 */
    contentCleaning?: RegExp
    /** 用户消息识别正则 */
    userMessageIdentifier?: RegExp
    /** AI消息识别正则 */
    assistantMessageIdentifier?: RegExp
  }
  
  /** 平台特有配置 */
  customConfig?: {
    /** 是否支持流式输出 */
    supportsStreaming?: boolean
    /** 消息加载延迟（毫秒） */
    messageLoadDelay?: number
    /** 特殊处理标识 */
    specialHandling?: string[]
  }
}

/**
 * 选择器匹配结果
 */
export interface SelectorMatchResult {
  element: Element | null
  selector: string
  index: number
}

/**
 * 正则匹配结果
 */
export interface RegexMatchResult {
  matched: boolean
  groups?: RegExpMatchArray
  content?: string
}

/**
 * 平台检测结果
 */
export interface PlatformDetectionResult {
  platform: PlatformConfig | null
  confidence: number
  matchedPatterns: string[]
}

/**
 * 消息提取结果
 */
export interface MessageExtractionResult {
  role: 'user' | 'assistant'
  content: string
  timestamp: number
  element: Element
}

/**
 * 通用正则表达式模式
 */
export const CommonRegexPatterns = {
  /** 清理HTML标签 */
  HTML_TAGS: /<[^>]*>/g,
  /** 清理多余空白字符 */
  EXTRA_WHITESPACE: /\s+/g,
  /** 匹配代码块 */
  CODE_BLOCK: /```[\s\S]*?```/g,
  /** 匹配链接 */
  LINKS: /https?:\/\/[^\s]+/g,
  /** 匹配邮箱 */
  EMAIL: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
  /** 匹配时间戳 */
  TIMESTAMP: /\d{4}-\d{2}-\d{2}[\sT]\d{2}:\d{2}:\d{2}/g
} as const

/**
 * 通用选择器模式
 */
export const CommonSelectorPatterns = {
  /** 输入框通用模式 */
  INPUT_FIELD: [
    'textarea[placeholder*="message" i]',
    'textarea[placeholder*="prompt" i]',
    'div[contenteditable="true"]',
    'input[type="text"]',
    '[role="textbox"]'
  ],
  /** 发送按钮通用模式 */
  SEND_BUTTON: [
    'button[aria-label*="send" i]',
    'button[data-testid*="send" i]',
    'button[type="submit"]',
    'button:has(svg)',
    '[role="button"]:has(svg)'
  ],
  /** 消息容器通用模式 */
  MESSAGE_CONTAINER: [
    '[data-message-author-role]',
    '[data-testid*="message"]',
    '.message',
    '.chat-message',
    '[role="article"]'
  ]
} as const
