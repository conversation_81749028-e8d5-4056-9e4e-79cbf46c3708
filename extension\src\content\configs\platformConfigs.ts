import { PlatformConfig } from '../types/PlatformConfig'

/**
 * ChatGPT平台配置
 */
export const ChatGPTConfig: PlatformConfig = {
  name: 'ChatGPT',
  id: 'chatgpt',
  url: 'https://chat.openai.com',
  patterns: {
    hostname: /^chat\.openai\.com$/,
    validPath: /^\/c\//
  },
  selectors: {
    inputField: [
      '#prompt-textarea',
      'textarea[placeholder*="Message"]'
    ],
    sendButton: [
      'button[data-testid="send-button"]',
      'button[aria-label="Send prompt"]'
    ],
    messageContainer: [
      '[data-message-author-role]',
      '.group'
    ],
    userMessage: [
      '[data-message-author-role="user"]'
    ],
    assistantMessage: [
      '[data-message-author-role="assistant"]'
    ],
    conversationTitle: [
      'h1',
      '[data-testid="conversation-title"]'
    ]
  },
  regexPatterns: {
    messageExtraction: /^(.+)$/,
    contentCleaning: /^\s+|\s+$/g,
    userMessageIdentifier: /data-message-author-role="user"/,
    assistantMessageIdentifier: /data-message-author-role="assistant"/
  },
  customConfig: {
    supportsStreaming: true,
    messageLoadDelay: 500
  }
}

/**
 * Claude平台配置
 */
export const ClaudeConfig: PlatformConfig = {
  name: 'Claude',
  id: 'claude',
  url: 'https://claude.ai',
  patterns: {
    hostname: /^claude\.ai$/,
    validPath: /^\/chat|^\/c\//
  },
  selectors: {
    inputField: [
      'div[contenteditable="true"]'
    ],
    sendButton: [
      'button[aria-label*="Send"]',
      'button[type="submit"]'
    ],
    messageContainer: [
      '.font-claude-message',
      '[data-is-streaming]',
      '.message'
    ],
    userMessage: [
      '[data-testid="user-message"]',
      '.user-message'
    ],
    assistantMessage: [
      '.font-claude-message',
      '[data-testid="message-content"]'
    ],
    conversationTitle: [
      'h1',
      '[data-testid="conversation-title"]',
      '.conversation-title'
    ]
  },
  regexPatterns: {
    messageExtraction: /^(.+)$/,
    contentCleaning: /^\s+|\s+$/g,
    userMessageIdentifier: /data-testid="user-message"/,
    assistantMessageIdentifier: /font-claude-message/
  },
  customConfig: {
    supportsStreaming: true,
    messageLoadDelay: 300
  }
}

/**
 * DeepSeek平台配置
 */
export const DeepSeekConfig: PlatformConfig = {
  name: 'DeepSeek',
  id: 'deepseek',
  url: 'https://chat.deepseek.com',
  patterns: {
    hostname: /^chat\.deepseek\.com$/
  },
  selectors: {
    inputField: [
      '#chat-input',
      'textarea[placeholder*="DeepSeek"]',
      'textarea[placeholder*="发送消息"]',
      'textarea[placeholder*="输入"]',
      'textarea[placeholder*="message"]',
      'textarea[placeholder*="请输入"]'
    ],
    sendButton: [
      '.ds-button--primary',
      '[role="button"].ds-button',
      'button[aria-label*="发送"]',
      'button[aria-label*="Send"]',
      '.send-button',
      '[data-testid*="send"]'
    ],
    messageContainer: [
      '.ds-markdown',
      '._4f9bf79',
      '._9663006',
      '.message-item',
      '.chat-message',
      '.message-container',
      '.conversation'
    ],
    userMessage: [
      '._9663006',
      '.user-message'
    ],
    assistantMessage: [
      '.ds-markdown',
      '._4f9bf79',
      '.message-content',
      '.assistant-message'
    ],
    conversationTitle: [
      '.d8ed659a',
      '.chat-title',
      'h1'
    ]
  },
  regexPatterns: {
    messageExtraction: /^(.+)$/,
    contentCleaning: /^\s+|\s+$/g
  },
  customConfig: {
    supportsStreaming: true,
    messageLoadDelay: 400
  }
}

/**
 * Gemini平台配置
 */
export const GeminiConfig: PlatformConfig = {
  name: 'Gemini',
  id: 'gemini',
  url: 'https://gemini.google.com',
  patterns: {
    hostname: /^gemini\.google\.com$/
  },
  selectors: {
    inputField: [
      'rich-textarea[placeholder*="Enter a prompt"]',
      'textarea[aria-label*="Message"]'
    ],
    sendButton: [
      'button[aria-label*="Send"]',
      'button[data-testid="send-button"]'
    ],
    messageContainer: [
      'message-content',
      '.conversation-container'
    ],
    conversationTitle: [
      '.conversation-title',
      'h1'
    ]
  },
  regexPatterns: {
    messageExtraction: /^(.+)$/,
    contentCleaning: /^\s+|\s+$/g
  },
  customConfig: {
    supportsStreaming: true,
    messageLoadDelay: 600
  }
}

/**
 * Kimi平台配置
 */
export const KimiConfig: PlatformConfig = {
  name: 'Kimi',
  id: 'kimi',
  url: 'https://kimi.moonshot.cn',
  patterns: {
    hostname: /^(.*\.)?(kimi\.moonshot\.cn|kimi\.com|www\.kimi\.com)$/
  },
  selectors: {
    inputField: [
      '[data-lexical-editor="true"]',
      '.chat-input-editor[contenteditable="true"]',
      '.chat-input-editor',
      'textarea[placeholder*="请输入"]',
      'textarea[placeholder*="输入"]',
      'textarea[placeholder*="message"]'
    ],
    sendButton: [
      '.send-button-container:not(.disabled) .send-button',
      '.send-button:not(.disabled)',
      '.chat-editor-action .send-button-container:not(.disabled) .send-button',
      'button[aria-label*="发送"]',
      'button[aria-label*="Send"]',
      '.send-btn',
      '[data-testid*="send"]'
    ],
    messageContainer: [
      '.chat-content-item',
      '.segment',
      '.message-item',
      '.conversation-item',
      '.message-wrapper',
      '.chat-message'
    ],
    userMessage: [
      '.chat-content-item-user',
      '.segment-user',
      '.user-content'
    ],
    assistantMessage: [
      '.chat-content-item-assistant',
      '.segment-assistant',
      '.markdown-container .markdown'
    ],
    conversationTitle: [
      '.chat-header-content h2',
      '.chat-name',
      '.chat-title',
      'h1'
    ]
  },
  regexPatterns: {
    messageExtraction: /^(.+)$/,
    contentCleaning: /^\s+|\s+$/g
  },
  customConfig: {
    supportsStreaming: true,
    messageLoadDelay: 500,
    specialHandling: ['lexical-editor']
  }
}

/**
 * 所有平台配置的映射
 */
export const PlatformConfigs = {
  chatgpt: ChatGPTConfig,
  claude: ClaudeConfig,
  deepseek: DeepSeekConfig,
  gemini: GeminiConfig,
  kimi: KimiConfig
} as const

/**
 * 平台配置数组（用于遍历检测）
 */
export const PlatformConfigList = Object.values(PlatformConfigs)
