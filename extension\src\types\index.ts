// 用户相关类型
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  plan: 'free' | 'pro' | 'vip';
  createdAt: Date;
  updatedAt: Date;
}

// AI平台类型
export type AIPlatform =
  | 'chatgpt'
  | 'deepseek'
  | 'claude'
  | 'gemini'
  | 'kimi'
  | 'poe'
  | 'perplexity'
  | 'you';

export interface Platform {
  id: AIPlatform;
  name: string;
  url: string;
  enabled: boolean;
  selectors: {
    inputField: string;
    sendButton: string;
    messageContainer: string;
  };
}

// 提示词类型
export interface Prompt {
  id: string;
  content: string;
  platform: AIPlatform;
  timestamp: number;
  tags: string[];
  isFavorite: boolean;
  category?: string;
}

// 对话类型
export interface Conversation {
  id: string;
  platform: AIPlatform;
  title: string;
  messages: Message[];
  createdAt: number;
  updatedAt: number;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

// 设置类型
export interface Settings {
  syncEnabled: boolean;
  autoSync: boolean;
  platforms: Platform[];
  shortcuts: {
    openPopup: string;
    quickSync: string;
  };
  theme: 'light' | 'dark' | 'system';
  language: 'zh' | 'en';
}

// 同步设置
export interface SyncSettings {
  enabled: boolean;
  platforms: AIPlatform[];
  autoCapture: boolean;
  realTimeSync: boolean;
}

// 应用状态
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  syncSettings: SyncSettings;
  prompts: Prompt[];
  conversations: Conversation[];
  settings: Settings;
  isLoading: boolean;
  activeTab: string;
}

// 消息类型
export enum MessageType {
  SYNC_PROMPT = 'SYNC_PROMPT',
  GET_HISTORY = 'GET_HISTORY',
  SAVE_CONVERSATION = 'SAVE_CONVERSATION',
  UPDATE_SETTINGS = 'UPDATE_SETTINGS',
  CAPTURE_PROMPT = 'CAPTURE_PROMPT',
  INJECT_PROMPT = 'INJECT_PROMPT',
  EXTRACT_CONVERSATION = 'EXTRACT_CONVERSATION',
  CHECK_PAGE_VALIDITY = 'CHECK_PAGE_VALIDITY',
  GET_PLATFORM_INFO = 'GET_PLATFORM_INFO',
  SHOW_NOTIFICATION = 'SHOW_NOTIFICATION',

  // Favicon相关消息
  UPDATE_PLATFORM_FAVICON = 'UPDATE_PLATFORM_FAVICON',
  CHECK_PLATFORM_FAVICON = 'CHECK_PLATFORM_FAVICON',

  // 数据库操作消息类型
  DB_CHAT_HISTORY_CREATE = 'DB_CHAT_HISTORY_CREATE',
  DB_CHAT_HISTORY_GET_LIST = 'DB_CHAT_HISTORY_GET_LIST',
  DB_CHAT_HISTORY_GET_UNIQUE = 'DB_CHAT_HISTORY_GET_UNIQUE',
  DB_CHAT_HISTORY_SEARCH = 'DB_CHAT_HISTORY_SEARCH',
  DB_CHAT_HISTORY_UPDATE = 'DB_CHAT_HISTORY_UPDATE',
  DB_CHAT_HISTORY_DELETE = 'DB_CHAT_HISTORY_DELETE',
  DB_CHAT_HISTORY_GET_BY_UID = 'DB_CHAT_HISTORY_GET_BY_UID',
  DB_CHAT_HISTORY_GET_PROMPTS_WITH_PLATFORMS = 'DB_CHAT_HISTORY_GET_PROMPTS_WITH_PLATFORMS',

  DB_PLATFORM_GET_BY_NAME = 'DB_PLATFORM_GET_BY_NAME',
  DB_PLATFORM_GET_BY_DOMAIN = 'DB_PLATFORM_GET_BY_DOMAIN',
  DB_PLATFORM_GET_LIST = 'DB_PLATFORM_GET_LIST'
}

export interface ChromeMessage<T = any> {
  type: MessageType;
  payload: T;
  timestamp: number;
  tabId?: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
