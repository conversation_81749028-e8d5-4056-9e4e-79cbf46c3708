/**
 * 组件相关类型定义
 * 统一管理所有UI组件和注入器的类型和接口
 */

import { EventCallback, ContentEventType } from './events'
import { IEventBus } from './events'

/**
 * 组件生命周期状态
 */
export enum ComponentLifecycleState {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 已初始化 */
  INITIALIZED = 'initialized',
  /** 运行中 */
  RUNNING = 'running',
  /** 暂停中 */
  PAUSED = 'paused',
  /** 销毁中 */
  DESTROYING = 'destroying',
  /** 已销毁 */
  DESTROYED = 'destroyed'
}

/**
 * 组件配置基础接口
 */
export interface ComponentConfig {
  /** 组件ID */
  id?: string
  /** 组件名称 */
  name?: string
  /** 是否启用 */
  enabled?: boolean
  /** 调试模式 */
  debug?: boolean
  /** 自定义样式 */
  customStyles?: Record<string, string>
  /** 自定义属性 */
  customAttributes?: Record<string, string>
}

/**
 * 基础组件接口
 */
export interface IComponent {
  /** 组件ID */
  readonly id: string
  /** 组件名称 */
  readonly name: string
  /** 生命周期状态 */
  readonly state: ComponentLifecycleState
  /** 是否可见 */
  readonly isVisible: boolean
  /** DOM元素 */
  readonly element: HTMLElement | null

  /** 渲染组件 */
  render(): HTMLElement
  /** 显示组件 */
  show(): void
  /** 隐藏组件 */
  hide(): void
  /** 销毁组件 */
  destroy(): void
  /** 更新组件 */
  update(data?: any): void
  /** 获取组件状态 */
  getState(): ComponentLifecycleState
}

/**
 * 浮动气泡组件配置
 */
export interface FloatingBubbleConfig extends ComponentConfig {
  /** 初始位置 */
  initialPosition?: { x: number; y: number }
  /** 是否可拖拽 */
  draggable?: boolean
  /** 自动隐藏延迟（毫秒） */
  autoHideDelay?: number
  /** 最小尺寸 */
  minSize?: { width: number; height: number }
  /** 最大尺寸 */
  maxSize?: { width: number; height: number }
  /** 边界约束 */
  boundary?: 'viewport' | 'parent' | HTMLElement
}

/**
 * 浮动气泡组件接口
 */
export interface IFloatingBubble extends IComponent {
  /** 移动到指定位置 */
  moveTo(x: number, y: number): void
  /** 移动到输入框附近 */
  moveToInputField(inputElement: HTMLElement): void
  /** 设置拖拽状态 */
  setDraggable(draggable: boolean): void
  /** 获取当前位置 */
  getPosition(): { x: number; y: number }
}

/**
 * 存档按钮组件配置
 */
export interface ArchiveButtonConfig extends ComponentConfig {
  /** 按钮文本 */
  text?: string
  /** 按钮图标 */
  icon?: string
  /** 按钮位置 */
  position?: 'top' | 'bottom' | 'left' | 'right'
  /** 相对于输入框的偏移 */
  offset?: { x: number; y: number }
  /** 按钮样式 */
  style?: 'primary' | 'secondary' | 'outline' | 'ghost'
}

/**
 * 存档按钮组件接口
 */
export interface IArchiveButton extends IComponent {
  /** 设置按钮状态 */
  setState(state: 'enabled' | 'disabled' | 'loading'): void
  /** 设置按钮文本 */
  setText(text: string): void
  /** 设置按钮图标 */
  setIcon(icon: string): void
  /** 获取按钮状态 */
  getButtonState(): 'enabled' | 'disabled' | 'loading'
}

/**
 * 历史气泡组件配置
 */
export interface HistoryBubbleConfig extends ComponentConfig {
  /** 最大显示条目数 */
  maxItems?: number
  /** 是否支持搜索 */
  searchable?: boolean
  /** 是否支持分页 */
  paginated?: boolean
  /** 每页条目数 */
  itemsPerPage?: number
  /** 模态框尺寸 */
  modalSize?: 'small' | 'medium' | 'large' | 'fullscreen'
}

/**
 * 历史气泡组件接口
 */
export interface IHistoryBubble extends IComponent {
  /** 打开历史模态框 */
  openModal(): void
  /** 关闭历史模态框 */
  closeModal(): void
  /** 刷新历史数据 */
  refreshHistory(): Promise<void>
  /** 搜索历史记录 */
  searchHistory(query: string): void
  /** 获取历史记录 */
  getHistory(): any[]
}

/**
 * 注入器基础接口
 */
export interface IInject {
  /** 注入器ID */
  readonly id: string
  /** 注入器名称 */
  readonly name: string
  /** 生命周期状态 */
  readonly state: ComponentLifecycleState
  /** 是否已注入 */
  readonly isInjected: boolean

  /** 执行注入 */
  inject(): Promise<void>
  /** 取消注入 */
  uninject(): Promise<void>
  /** 销毁注入器 */
  destroy(): void
  /** 获取注入状态 */
  getState(): ComponentLifecycleState
}

/**
 * 注入器配置基础接口
 */
export interface InjectConfig {
  /** 注入器ID */
  id?: string
  /** 注入器名称 */
  name?: string
  /** 是否启用 */
  enabled?: boolean
  /** 调试模式 */
  debug?: boolean
  /** 注入延迟（毫秒） */
  injectDelay?: number
  /** 重试次数 */
  retryCount?: number
  /** 重试间隔（毫秒） */
  retryInterval?: number
}

/**
 * 浮动气泡注入器配置
 */
export interface FloatingBubbleInjectConfig extends InjectConfig {
  /** 目标选择器 */
  targetSelector?: string
  /** 组件配置 */
  componentConfig?: FloatingBubbleConfig
  /** 事件监听配置 */
  eventListeners?: {
    [key in ContentEventType]?: EventCallback
  }
}

/**
 * 存档按钮注入器配置
 */
export interface ArchiveButtonInjectConfig extends InjectConfig {
  /** 输入框选择器 */
  inputSelector?: string
  /** 发送按钮选择器 */
  sendButtonSelector?: string
  /** 组件配置 */
  componentConfig?: ArchiveButtonConfig
}

/**
 * 历史气泡注入器配置
 */
export interface HistoryBubbleInjectConfig extends InjectConfig {
  /** 触发器选择器 */
  triggerSelector?: string
  /** 组件配置 */
  componentConfig?: HistoryBubbleConfig
}

/**
 * 组件工厂接口
 */
export interface IComponentFactory {
  /** 创建浮动气泡组件 */
  createFloatingBubble(config?: FloatingBubbleConfig): IFloatingBubble
  /** 创建存档按钮组件 */
  createArchiveButton(config?: ArchiveButtonConfig): IArchiveButton
  /** 创建历史气泡组件 */
  createHistoryBubble(config?: HistoryBubbleConfig): IHistoryBubble
}

/**
 * 注入器工厂接口
 */
export interface IInjectFactory {
  /** 创建浮动气泡注入器 */
  createFloatingBubbleInject(
    eventBus: IEventBus,
    config?: FloatingBubbleInjectConfig
  ): IInject
  /** 创建存档按钮注入器 */
  createArchiveButtonInject(
    eventBus: IEventBus,
    config?: ArchiveButtonInjectConfig
  ): IInject
  /** 创建历史气泡注入器 */
  createHistoryBubbleInject(
    eventBus: IEventBus,
    config?: HistoryBubbleInjectConfig
  ): IInject
}

/**
 * 组件管理器接口
 */
export interface IComponentManager {
  /** 注册组件 */
  registerComponent(component: IComponent): void
  /** 注销组件 */
  unregisterComponent(id: string): void
  /** 获取组件 */
  getComponent<T extends IComponent>(id: string): T | null
  /** 获取所有组件 */
  getAllComponents(): IComponent[]
  /** 销毁所有组件 */
  destroyAll(): void
}

/**
 * 注入器管理器接口
 */
export interface IInjectManager {
  /** 注册注入器 */
  registerInject(inject: IInject): void
  /** 注销注入器 */
  unregisterInject(id: string): void
  /** 获取注入器 */
  getInject(id: string): IInject | null
  /** 获取所有注入器 */
  getAllInjects(): IInject[]
  /** 执行所有注入 */
  injectAll(): Promise<void>
  /** 取消所有注入 */
  uninjectAll(): Promise<void>
  /** 销毁所有注入器 */
  destroyAll(): void
}

/**
 * 拖拽处理器配置
 */
export interface DragHandlerConfig {
  /** 拖拽手柄选择器 */
  handleSelector?: string
  /** 边界约束 */
  boundary?: 'viewport' | 'parent' | HTMLElement
  /** 是否启用网格对齐 */
  snapToGrid?: boolean
  /** 网格大小 */
  gridSize?: number
  /** 拖拽阈值 */
  threshold?: number
}

/**
 * 拖拽处理器接口
 */
export interface IDragHandler {
  /** 启用拖拽 */
  enable(): void
  /** 禁用拖拽 */
  disable(): void
  /** 是否启用 */
  isEnabled(): boolean
  /** 设置边界 */
  setBoundary(boundary: 'viewport' | 'parent' | HTMLElement): void
  /** 销毁处理器 */
  destroy(): void
}
