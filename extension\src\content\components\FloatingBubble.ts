/**
 * 浮动气泡UI组件
 * 专注于UI渲染和样式，不包含业务逻辑
 */

import { BaseComponent } from './BaseComponent'
import { 
  IFloatingBubble, 
  FloatingBubbleConfig 
} from '../types/components'

/**
 * 浮动气泡UI组件实现
 */
export class FloatingBubble extends BaseComponent implements IFloatingBubble {
  private position: { x: number; y: number } = { x: 20, y: 20 }
  private isDraggable: boolean = true
  private isHovered: boolean = false
  private animationFrame: number | null = null

  constructor(config: FloatingBubbleConfig = {}) {
    super({
      name: 'FloatingBubble',
      ...config
    })

    // 设置初始位置
    if (config.initialPosition) {
      this.position = { ...config.initialPosition }
    }

    this.isDraggable = config.draggable ?? true

    // 从localStorage恢复位置
    this.restorePosition()
  }

  /**
   * 渲染组件
   */
  render(): HTMLElement {
    const bubble = this.createElement('div', {
      'id': 'echosync-floating-bubble',
      'data-component': 'floating-bubble',
      'role': 'button',
      'tabindex': '0',
      'aria-label': 'EchoSync浮动气泡'
    })

    // 设置基础样式
    this.applyStyles(bubble)

    // 添加图标
    this.renderIcon(bubble)

    // 应用自定义样式和属性
    this.applyCustomStyles(bubble)
    this.applyCustomAttributes(bubble)

    // 设置初始位置
    this.updatePosition(bubble)

    return bubble
  }

  /**
   * 移动到指定位置
   */
  moveTo(x: number, y: number): void {
    this.position = { x, y }
    
    if (this._element) {
      this.updatePosition(this._element)
      this.savePosition()
    }

    this.log('debug', `Moved to position: (${x}, ${y})`)
  }

  /**
   * 移动到输入框附近
   */
  moveToInputField(inputElement: HTMLElement): void {
    if (!inputElement) {
      this.log('warn', 'Input element not provided')
      return
    }

    const rect = inputElement.getBoundingClientRect()
    const bubbleSize = 30
    const margin = 8

    // 计算位置（输入框左上方）
    const x = Math.max(margin, rect.left - bubbleSize - margin)
    const y = Math.max(margin, rect.top - bubbleSize - margin)

    this.moveTo(x, y)
    this.log('debug', 'Moved to input field position')
  }

  /**
   * 设置拖拽状态
   */
  setDraggable(draggable: boolean): void {
    this.isDraggable = draggable
    
    if (this._element) {
      this._element.style.cursor = draggable ? 'grab' : 'pointer'
    }

    this.log('debug', `Draggable set to: ${draggable}`)
  }

  /**
   * 获取当前位置
   */
  getPosition(): { x: number; y: number } {
    return { ...this.position }
  }

  /**
   * 设置悬停状态
   */
  setHovered(hovered: boolean): void {
    if (this.isHovered === hovered) return

    this.isHovered = hovered
    
    if (this._element) {
      this.updateHoverState(this._element)
    }
  }

  /**
   * 边界回弹
   */
  snapToBoundary(): void {
    if (!this._element) return

    const rect = this._element.getBoundingClientRect()
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const bubbleSize = 30

    let newX = this.position.x
    let newY = this.position.y

    // 检查边界并调整位置
    if (rect.left < 0) newX = 0
    if (rect.right > windowWidth) newX = windowWidth - bubbleSize
    if (rect.top < 0) newY = 0
    if (rect.bottom > windowHeight) newY = windowHeight - bubbleSize

    // 如果位置有变化，应用回弹动画
    if (newX !== this.position.x || newY !== this.position.y) {
      this.animateToPosition(newX, newY)
    }
  }

  /**
   * 动画移动到指定位置
   */
  animateToPosition(x: number, y: number): void {
    if (!this._element) return

    // 取消之前的动画
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
    }

    const startX = this.position.x
    const startY = this.position.y
    const deltaX = x - startX
    const deltaY = y - startY
    const duration = 300 // 毫秒
    const startTime = performance.now()

    const animate = (currentTime: number) => {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // 使用缓动函数
      const easeProgress = this.easeOutCubic(progress)
      
      const currentX = startX + deltaX * easeProgress
      const currentY = startY + deltaY * easeProgress
      
      this.position = { x: currentX, y: currentY }
      this.updatePosition(this._element!)
      
      if (progress < 1) {
        this.animationFrame = requestAnimationFrame(animate)
      } else {
        this.animationFrame = null
        this.savePosition()
      }
    }

    this.animationFrame = requestAnimationFrame(animate)
  }

  /**
   * 应用基础样式
   */
  private applyStyles(element: HTMLElement): void {
    element.style.cssText = `
      position: fixed;
      width: 30px;
      height: 30px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: ${this.isDraggable ? 'grab' : 'pointer'};
      z-index: 10000;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: white;
      opacity: 0.9;
      user-select: none;
      border: none;
      outline: none;
    `
  }

  /**
   * 渲染图标
   */
  private renderIcon(container: HTMLElement): void {
    const iconContainer = this.createElement('div', {
      'class': 'bubble-content'
    }, {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: '100%'
    })

    iconContainer.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
      </svg>
    `

    container.appendChild(iconContainer)
  }

  /**
   * 更新位置
   */
  private updatePosition(element: HTMLElement): void {
    element.style.left = `${this.position.x}px`
    element.style.top = `${this.position.y}px`
    element.style.right = 'auto'
    element.style.bottom = 'auto'
  }

  /**
   * 更新悬停状态
   */
  private updateHoverState(element: HTMLElement): void {
    if (this.isHovered) {
      element.style.transform = 'scale(1.1)'
      element.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.25)'
    } else {
      element.style.transform = 'scale(1)'
      element.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)'
    }
  }

  /**
   * 保存位置到localStorage
   */
  private savePosition(): void {
    try {
      const positionData = {
        top: `${this.position.y}px`,
        left: `${this.position.x}px`,
        right: 'auto'
      }
      localStorage.setItem('echosync-bubble-position', JSON.stringify(positionData))
    } catch (error) {
      this.log('warn', 'Failed to save position:', error)
    }
  }

  /**
   * 从localStorage恢复位置
   */
  private restorePosition(): void {
    try {
      const savedPosition = localStorage.getItem('echosync-bubble-position')
      if (savedPosition) {
        const position = JSON.parse(savedPosition)
        if (position.left && position.top) {
          this.position.x = parseInt(position.left)
          this.position.y = parseInt(position.top)
        }
      }
    } catch (error) {
      this.log('warn', 'Failed to restore position:', error)
    }
  }

  /**
   * 缓动函数
   */
  private easeOutCubic(t: number): number {
    return 1 - Math.pow(1 - t, 3)
  }

  /**
   * 组件销毁时的清理
   */
  protected onDestroy(): void {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
      this.animationFrame = null
    }
  }

  /**
   * 组件更新
   */
  protected onUpdate(data?: any): void {
    if (data?.position) {
      this.moveTo(data.position.x, data.position.y)
    }
    
    if (data?.draggable !== undefined) {
      this.setDraggable(data.draggable)
    }
    
    if (data?.hovered !== undefined) {
      this.setHovered(data.hovered)
    }
  }
}
