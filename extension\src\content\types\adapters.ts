/**
 * 适配器相关类型定义
 * 统一管理所有适配器相关的类型和接口
 */

import { Conversation } from '@/types'
import { Platform } from '@/types/database_entity'
import { IEventBus, EventCallback, ContentEventType } from './events'
import { IComponentManager, IInjectManager } from './components'

/**
 * 适配器状态枚举
 */
export enum AdapterState {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 已初始化 */
  INITIALIZED = 'initialized',
  /** 运行中 */
  RUNNING = 'running',
  /** 暂停中 */
  PAUSED = 'paused',
  /** 错误状态 */
  ERROR = 'error',
  /** 销毁中 */
  DESTROYING = 'destroying',
  /** 已销毁 */
  DESTROYED = 'destroyed'
}

/**
 * 选择器配置接口
 */
export interface SelectorConfig {
  /** 输入框选择器 */
  inputField: string[]
  /** 发送按钮选择器 */
  sendButton: string[]
  /** 消息容器选择器 */
  messageContainer: string[]
  /** 输入容器选择器 */
  inputContainer: string[]
  /** 用户消息选择器 */
  userMessage?: string[]
  /** AI回答消息选择器 */
  assistantMessage?: string[]
  /** 对话标题选择器 */
  conversationTitle?: string[]
}

/**
 * 元素类型枚举
 */
export enum ElementType {
  INPUT_FIELD = 'inputField',
  SEND_BUTTON = 'sendButton',
  MESSAGE_CONTAINER = 'messageContainer',
  INPUT_CONTAINER = 'inputContainer',
  USER_MESSAGE = 'userMessage',
  ASSISTANT_MESSAGE = 'assistantMessage',
  CONVERSATION_TITLE = 'conversationTitle'
}

/**
 * 平台配置接口
 */
export interface PlatformConfig {
  /** 平台基本信息 */
  name: string
  id: string
  url: string
  
  /** 平台识别模式 */
  patterns: {
    /** 主机名匹配正则 */
    hostname: RegExp
    /** 有效路径匹配正则（可选） */
    validPath?: RegExp
  }
  
  /** DOM选择器配置 */
  selectors: SelectorConfig
  
  /** 正则表达式模式配置 */
  regexPatterns?: {
    /** 消息内容提取正则 */
    messageExtraction?: RegExp
    /** 标题提取正则 */
    titleExtraction?: RegExp
    /** 内容清理正则 */
    contentCleaning?: RegExp
    /** 用户消息识别正则 */
    userMessageIdentifier?: RegExp
    /** AI消息识别正则 */
    assistantMessageIdentifier?: RegExp
  }
  
  /** 平台特有配置 */
  customConfig?: {
    /** 是否支持流式输出 */
    supportsStreaming?: boolean
    /** 消息加载延迟（毫秒） */
    messageLoadDelay?: number
    /** 特殊处理标识 */
    specialHandling?: string[]
  }
}

/**
 * 选择器匹配结果
 */
export interface SelectorMatchResult {
  element: Element | null
  selector: string
  index: number
}

/**
 * 正则匹配结果
 */
export interface RegexMatchResult {
  matched: boolean
  groups?: RegExpMatchArray
  content?: string
}

/**
 * 平台检测结果
 */
export interface PlatformDetectionResult {
  platform: PlatformConfig | null
  confidence: number
  matchedPatterns: string[]
}

/**
 * 消息提取结果
 */
export interface MessageExtractionResult {
  role: 'user' | 'assistant'
  content: string
  timestamp: number
  element: Element
}

/**
 * 适配器初始化选项
 */
export interface AdapterInitOptions {
  /** 是否启用调试模式 */
  debug?: boolean
  /** 初始化超时时间（毫秒） */
  timeout?: number
  /** 是否自动重试 */
  autoRetry?: boolean
  /** 重试次数 */
  retryCount?: number
  /** 重试间隔（毫秒） */
  retryInterval?: number
}

/**
 * 基础适配器接口
 */
export interface IBaseAdapter {
  /** 适配器ID */
  readonly id: string
  /** 适配器名称 */
  readonly name: string
  /** 适配器状态 */
  readonly state: AdapterState
  /** 平台配置 */
  readonly config: PlatformConfig
  /** 当前平台信息 */
  readonly currentPlatform: Platform | null

  /** 初始化适配器 */
  initialize(options?: AdapterInitOptions): Promise<void>
  /** 销毁适配器 */
  destroy(): void
  /** 获取适配器状态 */
  getState(): AdapterState
  /** 获取平台配置 */
  getConfig(): PlatformConfig
  /** 设置当前平台信息 */
  setCurrentPlatform(platform: Platform | null): void
  /** 检查是否为有效页面 */
  isValidPage(): boolean
}

/**
 * AI适配器接口
 */
export interface IAIAdapter extends IBaseAdapter {
  /** 事件总线 */
  readonly eventBus: IEventBus
  /** 组件管理器 */
  readonly componentManager: IComponentManager
  /** 注入器管理器 */
  readonly injectManager: IInjectManager

  /** 提取对话内容 */
  extractConversation(): Promise<Conversation | null>
  /** 提取最新回答 */
  extractLatestAnswer(): Promise<string | null>
  /** 获取当前输入内容 */
  getCurrentInput(): string
  /** 注入提示词 */
  injectPrompt(prompt: string): Promise<void>
  /** 获取元素 */
  getElement(type: ElementType, forceRefresh?: boolean): HTMLElement | null
  /** 添加事件监听器 */
  addEventListener(event: ContentEventType, callback: EventCallback): void
  /** 移除事件监听器 */
  removeEventListener(event: ContentEventType, callback: EventCallback): void
}

/**
 * 适配器工厂接口
 */
export interface IAdapterFactory {
  /** 创建适配器 */
  createAdapter(platformId: string): IAIAdapter | null
  /** 注册适配器类 */
  registerAdapter(platformId: string, adapterClass: new () => IAIAdapter): void
  /** 获取支持的平台列表 */
  getSupportedPlatforms(): string[]
}

/**
 * 平台检测器接口
 */
export interface IPlatformDetector {
  /** 检测当前平台 */
  detectCurrentPlatform(): PlatformDetectionResult
  /** 注册平台配置 */
  registerPlatform(config: PlatformConfig): void
  /** 获取所有平台配置 */
  getAllPlatforms(): PlatformConfig[]
  /** 清除缓存 */
  clearCache(): void
}

/**
 * 元素管理器接口
 */
export interface IElementManager {
  /** 获取元素 */
  getElement(type: ElementType, selectors: string[], forceRefresh?: boolean): HTMLElement | null
  /** 缓存元素 */
  cacheElement(type: ElementType, element: HTMLElement): void
  /** 清除缓存 */
  clearCache(): void
  /** 销毁管理器 */
  destroy(): void
}

/**
 * 内容捕获器接口
 */
export interface IContentCapturer {
  /** 捕获对话内容 */
  captureConversation(selectors: SelectorConfig): Promise<Conversation | null>
  /** 捕获页面元素 */
  captureElements(selectors: SelectorConfig): Map<ElementType, HTMLElement>
  /** 提取消息内容 */
  extractMessages(container: HTMLElement, config: PlatformConfig): MessageExtractionResult[]
  /** 清理内容 */
  cleanContent(content: string, patterns?: RegExp[]): string
}

/**
 * 内容监听器接口
 */
export interface IContentListener {
  /** 开始监听 */
  startListening(selectors: SelectorConfig): void
  /** 停止监听 */
  stopListening(): void
  /** 是否正在监听 */
  isListening(): boolean
  /** 销毁监听器 */
  destroy(): void
}

/**
 * 适配器管理器接口
 */
export interface IAdapterManager {
  /** 当前适配器 */
  readonly currentAdapter: IAIAdapter | null
  /** 适配器状态 */
  readonly state: AdapterState

  /** 初始化管理器 */
  initialize(): Promise<void>
  /** 创建适配器 */
  createAdapter(platformId: string): Promise<IAIAdapter | null>
  /** 切换适配器 */
  switchAdapter(platformId: string): Promise<void>
  /** 销毁当前适配器 */
  destroyCurrentAdapter(): void
  /** 销毁管理器 */
  destroy(): void
}

/**
 * 适配器配置选项
 */
export interface AdapterConfigOptions {
  /** 选择器配置 */
  selectors?: Partial<SelectorConfig>
  /** 正则表达式配置 */
  regexPatterns?: PlatformConfig['regexPatterns']
  /** 自定义配置 */
  customConfig?: PlatformConfig['customConfig']
  /** 是否启用调试 */
  debug?: boolean
}

/**
 * 适配器性能指标
 */
export interface AdapterPerformanceMetrics {
  /** 初始化时间（毫秒） */
  initializationTime: number
  /** 元素查找平均时间（毫秒） */
  averageElementFindTime: number
  /** 内容提取平均时间（毫秒） */
  averageExtractionTime: number
  /** 事件处理平均时间（毫秒） */
  averageEventHandlingTime: number
  /** 内存使用量（字节） */
  memoryUsage: number
  /** 错误计数 */
  errorCount: number
  /** 成功率（百分比） */
  successRate: number
}

/**
 * 适配器错误类型
 */
export enum AdapterErrorType {
  /** 初始化错误 */
  INITIALIZATION_ERROR = 'initialization_error',
  /** 平台检测错误 */
  PLATFORM_DETECTION_ERROR = 'platform_detection_error',
  /** 元素查找错误 */
  ELEMENT_FIND_ERROR = 'element_find_error',
  /** 内容提取错误 */
  CONTENT_EXTRACTION_ERROR = 'content_extraction_error',
  /** 事件处理错误 */
  EVENT_HANDLING_ERROR = 'event_handling_error',
  /** 配置错误 */
  CONFIGURATION_ERROR = 'configuration_error',
  /** 网络错误 */
  NETWORK_ERROR = 'network_error',
  /** 未知错误 */
  UNKNOWN_ERROR = 'unknown_error'
}

/**
 * 适配器错误信息
 */
export interface AdapterError extends Error {
  /** 错误类型 */
  type: AdapterErrorType
  /** 错误代码 */
  code?: string
  /** 错误上下文 */
  context?: any
  /** 错误时间戳 */
  timestamp: number
  /** 适配器ID */
  adapterId: string
}

/**
 * 适配器日志级别
 */
export enum AdapterLogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

/**
 * 适配器日志条目
 */
export interface AdapterLogEntry {
  /** 日志级别 */
  level: AdapterLogLevel
  /** 日志消息 */
  message: string
  /** 时间戳 */
  timestamp: number
  /** 适配器ID */
  adapterId: string
  /** 上下文数据 */
  context?: any
  /** 错误信息 */
  error?: Error
}
