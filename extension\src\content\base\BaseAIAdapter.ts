import { Conversation } from '@/types'
import { Platform } from '@/types/database_entity'
import { PlatformConfig, SelectorMatchResult, RegexMatchResult, MessageExtractionResult } from '../types/PlatformConfig'
import { DOMUtils } from './DOMUtils'
import { FloatingBubble } from './FloatingBubble'
import { ArchiveButton } from './ArchiveButton'
import { DragHandler } from './DragHandler'
import { InputManager } from './InputManager'
import { HistoryManager } from './HistoryManager'
import { EventBus, eventBus } from '../shared/EventBus'
import { EventCallback, InputEvent, PageEvent, ContentEventType } from '../shared/EventEnum'
import { ElementManager, SelectorConfig, ElementType } from './ElementManager'
import { CommonSelectors } from '../configs/CommonSelectors'

/**
 * 重构后的AI适配器基类
 * 作为 content 模块的核心应用上下文，统一管理组件间交互
 */
export abstract class BaseAIAdapter {
  // 平台配置
  protected config: PlatformConfig
  protected currentPlatform: Platform | null = null

  // 核心系统组件
  protected eventBus: EventBus
  protected elementManager: ElementManager

  // UI组件实例
  protected floatingBubble: FloatingBubble
  protected archiveButton: ArchiveButton
  protected dragHandler: DragHandler | null = null
  protected inputManager: InputManager
  protected historyManager: HistoryManager

  constructor(config: PlatformConfig) {
    this.config = config

    // 初始化核心系统
    this.eventBus = eventBus
    this.elementManager = new ElementManager(this.eventBus, process.env.NODE_ENV === 'development')

    // 初始化UI组件，传入适配器实例
    this.floatingBubble = new FloatingBubble()
    this.archiveButton = new ArchiveButton(this as any) // 传入适配器实例
    this.inputManager = new InputManager(undefined, this as any) // 传入适配器实例
    this.historyManager = new HistoryManager()
  }

  /**
   * 初始化适配器
   */
  async initialize(): Promise<void> {
    console.log(`【EchoSync】Initializing ${this.config.name} adapter...`)

    try {
      // 等待页面加载
      await DOMUtils.waitForPageLoad()
      
      // 初始化UI组件
      await this.initializeUIComponents()
      
      // 设置事件监听
      this.setupEventListeners()
      
      // 执行平台特有的初始化
      await this.platformSpecificInit()
      
      console.log(`【EchoSync】${this.config.name} adapter initialized successfully`)
    } catch (error) {
      console.error(`【EchoSync】Error initializing ${this.config.name} adapter:`, error)
      throw error
    }
  }

  /**
   * 初始化UI组件
   */
  private async initializeUIComponents(): Promise<void> {
    // 创建悬浮气泡
    const bubble = this.floatingBubble.createFloatingBubble()

    if (bubble) {
      // 初始化拖拽处理
      this.dragHandler = new DragHandler(bubble)
      ;(bubble as any).dragHandler = this.dragHandler
    }

    // 查找并设置输入元素
    await this.setupInputElement()

    // 添加存档按钮
    await this.addArchiveButton()

    // 初始化时将气泡移动到输入框附近
    const inputElement = this.findElement(this.config.selectors.inputField)
    if (inputElement.element && bubble) {
      // 延迟一下确保DOM完全渲染
      setTimeout(() => {
        this.floatingBubble.moveToInputField(inputElement.element as HTMLElement)
        console.log('【EchoSync】Floating bubble positioned near input field on initialization')
      }, 100)
    }
  }

  /**
   * 设置输入元素
   */
  private async setupInputElement(): Promise<void> {
    // 等待输入框元素出现
    let inputResult = this.findElement(this.config.selectors.inputField)

    // 如果没有找到，等待一段时间后重试
    if (!inputResult.element) {
      console.log('【EchoSync】Input element not found, waiting...')
      await new Promise(resolve => setTimeout(resolve, 1000))
      inputResult = this.findElement(this.config.selectors.inputField)
    }

    if (inputResult.element) {
      // 转换配置格式以兼容现有的InputManager
      const legacySelectors = {
        inputField: inputResult.selector
      }
      await this.inputManager.findAndSetupInputElement(legacySelectors)
      console.log('【EchoSync】Input element setup for:', inputResult.selector)

      // 设置输入框聚焦监听
      this.inputManager.setupInputFocusListener(legacySelectors)
      console.log('【EchoSync】Input focus listener setup for:', inputResult.selector)
    } else {
      console.warn('【EchoSync】No input field found for setup after retry')
    }
  }

  /**
   * 添加存档按钮
   */
  private async addArchiveButton(): Promise<void> {
    // 查找输入框元素
    const inputResult = this.findElement(this.config.selectors.inputField)
    if (inputResult.element) {
      // 转换配置格式以兼容现有的ArchiveButton
      const legacySelectors = {
        inputField: inputResult.selector,
        sendButton: this.config.selectors.sendButton[0],
        messageContainer: this.config.selectors.messageContainer[0]
      }
      await this.archiveButton.addArchiveButton(legacySelectors)
      console.log('【EchoSync】Archive button added for input:', inputResult.selector)
    } else {
      console.warn('【EchoSync】No input field found for archive button')
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听输入变化
    this.setupInputChangeListener()

    // 监听页面变化
    this.setupPageChangeListener()

    // 监听窗口大小变化
    this.setupWindowResizeListener()

    // 设置平台特有的事件监听
    this.setupPlatformSpecificListeners()
  }

  /**
   * 设置输入变化监听
   */
  private setupInputChangeListener(): void {
    // 使用事件委托来监听输入框事件，避免时序问题
    document.addEventListener('input', (e) => {
      const target = e.target as HTMLElement
      if (this.isInputElement(target)) {
        // 发布输入变化事件
        this.eventBus.publish(InputEvent.CHANGED, {
          element: target,
          value: (target as HTMLInputElement).value || target.textContent || ''
        })
        this.onInputChange()
      }
    })

    document.addEventListener('focus', (e) => {
      const target = e.target as HTMLElement
      if (this.isInputElement(target)) {
        console.log('【EchoSync】Input element focused via document listener:', target)
        // 发布输入聚焦事件
        this.eventBus.publish(InputEvent.FOCUSED, {
          element: target,
          value: (target as HTMLInputElement).value || target.textContent || ''
        })
        this.onInputFocus()
      }
    }, true) // 使用捕获阶段确保能捕获到事件

    document.addEventListener('blur', (e) => {
      const target = e.target as HTMLElement
      if (this.isInputElement(target)) {
        // 发布输入失焦事件
        this.eventBus.publish(InputEvent.BLURRED, {
          element: target,
          value: (target as HTMLInputElement).value || target.textContent || ''
        })
      }
    })

    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      if (this.isInputElement(target)) {
        console.log('【EchoSync】Input element clicked via document listener:', target)
        // 延迟一下确保焦点已经设置
        setTimeout(() => {
          if (document.activeElement === target) {
            this.eventBus.publish(InputEvent.FOCUSED, {
              element: target,
              value: (target as HTMLInputElement).value || target.textContent || ''
            })
            this.onInputFocus()
          }
        }, 50)
      }
    })
  }

  /**
   * 检查元素是否是输入框
   */
  private isInputElement(element: HTMLElement): boolean {
    if (!element) return false

    // 检查是否匹配配置的选择器
    for (const selector of this.config.selectors.inputField) {
      try {
        if (element.matches(selector)) {
          return true
        }
      } catch (e) {
        // 忽略无效的选择器
        continue
      }
    }

    return false
  }

  /**
   * 设置页面变化监听
   */
  private setupPageChangeListener(): void {
    // 监听DOM变化以检测新消息
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // 发布页面变化事件
          this.eventBus.publish(PageEvent.CONTENT_CHANGED, {
            url: window.location.href,
            mutation
          })
          this.onPageContentChange(mutation)
        }
      })
    })

    const messageContainer = this.findElement(this.config.selectors.messageContainer)
    if (messageContainer.element) {
      observer.observe(messageContainer.element, {
        childList: true,
        subtree: true
      })
    }

    // 监听URL变化
    let currentUrl = window.location.href
    const urlObserver = new MutationObserver(() => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href
        this.eventBus.publish(PageEvent.URL_CHANGED, {
          oldUrl: currentUrl,
          newUrl: window.location.href
        })
      }
    })

    urlObserver.observe(document, {
      subtree: true,
      childList: true
    })
  }

  /**
   * 设置窗口大小变化监听
   */
  private setupWindowResizeListener(): void {
    let resizeTimeout: NodeJS.Timeout

    window.addEventListener('resize', () => {
      // 防抖处理，避免频繁调用
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        const inputElement = this.findElement(this.config.selectors.inputField)
        if (inputElement.element) {
          this.floatingBubble.moveToInputField(inputElement.element as HTMLElement)
          console.log('【EchoSync】Floating bubble repositioned after window resize')
        }
      }, 300)
    })
  }

  /**
   * 使用配置查找元素
   */
  protected findElement(selectors: string[]): SelectorMatchResult {
    for (let i = 0; i < selectors.length; i++) {
      const selector = selectors[i]
      const element = document.querySelector(selector)
      if (element) {
        return {
          element,
          selector,
          index: i
        }
      }
    }
    
    return {
      element: null,
      selector: '',
      index: -1
    }
  }

  /**
   * 使用配置查找所有匹配元素
   */
  protected findElements(selectors: string[]): Element[] {
    const elements: Element[] = []
    
    for (const selector of selectors) {
      const found = document.querySelectorAll(selector)
      elements.push(...Array.from(found))
    }
    
    return elements
  }

  /**
   * 正则表达式匹配
   */
  protected regexMatch(text: string, pattern: RegExp): RegexMatchResult {
    const match = text.match(pattern)
    return {
      matched: match !== null,
      groups: match || undefined,
      content: match?.[1] || match?.[0] || undefined
    }
  }

  /**
   * 清理文本内容
   */
  protected cleanContent(content: string): string {
    if (!content) return ''
    
    let cleaned = content
    
    // 使用配置的清理正则
    if (this.config.regexPatterns?.contentCleaning) {
      cleaned = cleaned.replace(this.config.regexPatterns.contentCleaning, ' ')
    }
    
    // 基本清理
    cleaned = cleaned.replace(/\s+/g, ' ').trim()
    
    return cleaned
  }

  /**
   * 注入提示词
   */
  async injectPrompt(prompt: string): Promise<void> {
    const inputElement = this.findElement(this.config.selectors.inputField)
    if (inputElement.element) {
      this.inputManager.injectPrompt(prompt)
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  /**
   * 获取当前输入内容
   */
  getCurrentInput(): string {
    const customInput = this.getCustomCurrentInput()
    if (customInput !== null) {
      return customInput
    }
    
    return this.inputManager.getCurrentInput()
  }

  /**
   * 获取平台名称
   */
  getPlatformName(): string {
    return this.config.name
  }

  /**
   * 获取平台配置
   */
  getConfig(): PlatformConfig {
    return this.config
  }

  /**
   * 通过元素管理器获取元素
   * @param type 元素类型
   * @param forceRefresh 是否强制刷新缓存
   * @returns 找到的元素或 null
   */
  getElement(type: ElementType, forceRefresh: boolean = false): HTMLElement | null {
    // 合并选择器
    const mergedSelectors = this.mergeSelectors()

    // 通过元素管理器获取元素
    return this.elementManager.getElement(type, mergedSelectors[type], forceRefresh)
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param callback 回调函数
   */
  addEventListener(event: ContentEventType, callback: EventCallback): void {
    this.eventBus.subscribe(event, callback)
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param callback 回调函数
   */
  removeEventListener(event: ContentEventType, callback: EventCallback): void {
    this.eventBus.unsubscribe(event, callback)
  }

  /**
   * 检查是否为有效页面
   */
  isValidPage(): boolean {
    const hostnameMatch = this.config.patterns.hostname.test(window.location.hostname)
    const pathMatch = this.config.patterns.validPath ? 
      this.config.patterns.validPath.test(window.location.pathname) : true
    
    return hostnameMatch && pathMatch
  }

  /**
   * 设置当前平台信息
   */
  setCurrentPlatform(platform: Platform | null): void {
    this.currentPlatform = platform
  }

  // 抽象方法 - 子类必须实现
  abstract extractConversation(): Promise<Conversation | null>
  protected abstract extractLatestAnswer(): Promise<string | null>

  /**
   * 获取平台特定的选择器配置
   * 子类必须实现此方法来提供平台特定的选择器
   */
  abstract getSelectors(): SelectorConfig

  /**
   * 合并通用选择器和平台特定选择器
   * 子类可以重写此方法来自定义合并逻辑
   */
  protected mergeSelectors(commonSelectors?: SelectorConfig): SelectorConfig {
    const platformSelectors = this.getSelectors()
    const common = commonSelectors || CommonSelectors.getCommonSelectors()

    // 平台特定选择器优先，然后是通用选择器
    return {
      inputField: [...platformSelectors.inputField, ...common.inputField],
      sendButton: [...platformSelectors.sendButton, ...common.sendButton],
      messageContainer: [...platformSelectors.messageContainer, ...common.messageContainer],
      inputContainer: [...platformSelectors.inputContainer, ...common.inputContainer]
    }
  }

  // 可重写的方法 - 子类可选择实现
  protected async platformSpecificInit(): Promise<void> {
    // 默认为空，子类可重写
  }

  protected setupPlatformSpecificListeners(): void {
    // 默认为空，子类可重写
  }

  protected onInputChange(): void {
    // 获取当前输入内容
    const inputContent = this.getCurrentInput()

    // 更新存档按钮状态
    this.archiveButton.updateArchiveButtonState(inputContent)

    console.log('【EchoSync】Input changed, archive button state updated')
  }

  protected onInputFocus(): void {
    // 当输入框获得焦点时，移动悬浮气泡到输入框附近
    const inputElement = this.findElement(this.config.selectors.inputField)
    if (inputElement.element) {
      this.floatingBubble.moveToInputField(inputElement.element as HTMLElement)
      console.log('【EchoSync】Floating bubble moved to input field on focus')
    }
  }

  protected onPageContentChange(_mutation: MutationRecord): void {
    // 页面内容变化时，检查是否需要重新定位气泡
    // 这对SPA应用很重要，因为输入框可能会重新渲染
    const inputElement = this.findElement(this.config.selectors.inputField)
    if (inputElement.element) {
      // 延迟一下确保DOM变化完成
      setTimeout(() => {
        this.floatingBubble.moveToInputField(inputElement.element as HTMLElement)
        console.log('【EchoSync】Floating bubble repositioned after page content change')
      }, 200)
    }
  }

  protected getCustomCurrentInput(): string | null {
    // 默认返回null，使用通用实现，子类可重写
    return null
  }

  /**
   * 销毁适配器
   */
  destroy(): void {
    // 清理核心系统
    this.elementManager.destroy()
    this.eventBus.clear()

    // 清理UI组件
    this.floatingBubble.destroy()
    this.archiveButton.destroy()
    this.dragHandler?.destroy()
    this.inputManager.destroy()
    this.historyManager.destroy()

    console.log(`【EchoSync】${this.config.name} adapter destroyed`)
  }
}
