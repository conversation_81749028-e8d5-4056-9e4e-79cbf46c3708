/**
 * 历史气泡注入器
 * 处理历史气泡的业务逻辑、数据获取和交互
 */

import { BaseInject } from './BaseInject'
import { HistoryBubble, HistoryItem } from '../components/HistoryBubble'
import { 
  IEventBus, 
  UIEvent,
  BusinessEvent 
} from '../types/events'
import { IAIAdapter } from '../types/adapters'
import { HistoryBubbleInjectConfig } from '../types/components'
import { MessagingService } from '@/lib/service/messagingService'
import { MessageType } from '@/types'
import { chatHistoryDatabaseProxy } from '@/lib/service/databaseProxy'

/**
 * 历史气泡注入器实现
 */
export class HistoryBubbleInject extends BaseInject {
  private component: HistoryBubble | null = null
  private historyData: HistoryItem[] = []
  private isDataLoading: boolean = false
  private lastUpdateTime: number = 0
  private updateThrottle: number = 1000 // 1秒节流
  private config: HistoryBubbleInjectConfig

  constructor(
    adapter: IAIAdapter,
    eventBus: IEventBus,
    config: HistoryBubbleInjectConfig = {}
  ) {
    super(adapter, eventBus, {
      name: 'HistoryBubbleInject',
      ...config
    })
    
    this.config = config
  }

  /**
   * 执行注入
   */
  async inject(): Promise<void> {
    try {
      // 创建UI组件
      this.component = new HistoryBubble(this.config.componentConfig)
      
      // 显示组件
      this.component.show()
      
      // 设置事件监听
      this.setupEventListeners()
      
      // 初始加载历史数据
      await this.loadHistoryData()
      
      this.log('info', 'HistoryBubble injected successfully')
      
    } catch (error) {
      this.log('error', 'Failed to inject HistoryBubble:', error)
      throw error
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.component?.element) return

    // 监听历史记录项点击事件
    this.addDOMEventListener(this.component.element, 'click', (e) => {
      const target = e.target as HTMLElement
      const historyItem = target.closest('.echosync-history-item') as HTMLElement
      
      if (historyItem) {
        this.handleHistoryItemClick(historyItem)
      }
    })

    // 监听键盘事件
    this.addDOMEventListener(this.component.element, 'keydown', (e) => {
      const target = e.target as HTMLElement
      
      if (e.key === 'Enter' || e.key === ' ') {
        const historyItem = target.closest('.echosync-history-item') as HTMLElement
        if (historyItem) {
          e.preventDefault()
          this.handleHistoryItemClick(historyItem)
        }
      }
    })

    // 监听分页按钮点击
    this.addDOMEventListener(this.component.element, 'click', (e) => {
      const target = e.target as HTMLElement
      
      if (target.classList.contains('echosync-pagination-btn')) {
        e.preventDefault()
        this.handlePaginationClick(target)
      }
    })

    // 监听UI事件
    this.addEventListner(UIEvent.BUBBLE_SHOW, (event) => {
      if (event.data.position) {
        this.showHistoryBubble(event.data.position)
      }
    })

    this.addEventListner(UIEvent.BUBBLE_HIDE, () => {
      this.hideHistoryBubble()
    })

    this.addEventListner(UIEvent.HISTORY_CLICK, () => {
      this.component?.openModal()
    })

    // 监听业务事件
    this.addEventListner(BusinessEvent.ARCHIVE_COMPLETED, () => {
      // 存档完成后刷新历史数据
      this.setTimeout(() => {
        this.loadHistoryData()
      }, 500)
    })

    // 监听模态框关闭事件
    this.addEventListner(UIEvent.MODAL_CLOSE, (event) => {
      if (event.data.type === 'history') {
        this.component?.closeModal()
      }
    })
  }

  /**
   * 显示历史气泡
   */
  private async showHistoryBubble(position?: { x: number; y: number }): Promise<void> {
    if (!this.component) return

    try {
      // 确保数据是最新的
      await this.loadHistoryData()

      // 查找锚点元素
      const anchorElement = document.getElementById('echosync-floating-bubble')
      if (anchorElement) {
        this.component.showBubble(anchorElement)
        this.log('debug', 'History bubble shown')
      }
    } catch (error) {
      this.log('error', 'Failed to show history bubble:', error)
    }
  }

  /**
   * 隐藏历史气泡
   */
  private hideHistoryBubble(): void {
    if (this.component) {
      this.component.hideBubble()
      this.log('debug', 'History bubble hidden')
    }
  }

  /**
   * 加载历史数据
   */
  private async loadHistoryData(): Promise<void> {
    // 节流处理
    const now = Date.now()
    if (this.isDataLoading || (now - this.lastUpdateTime) < this.updateThrottle) {
      return
    }

    this.isDataLoading = true
    this.lastUpdateTime = now

    try {
      // 从数据库获取历史记录
      const result = await chatHistoryDatabaseProxy.getUniqueChats({
        limit: this.config.componentConfig?.maxItems || 10,
        order_direction: 'DESC'
      })

      if (result.success && result.data) {
        // 转换数据格式
        this.historyData = result.data.map(item => ({
          id: item.id.toString(),
          chat_uid: item.chat_uid,
          chat_prompt: item.chat_prompt,
          platform_name: item.platform_name,
          create_time: item.create_time,
          platform_icon: this.getPlatformIcon(item.platform_name)
        }))

        // 更新组件数据
        this.component?.updateHistory(this.historyData)

        this.log('info', `Loaded ${this.historyData.length} history items`)
      } else {
        this.log('warn', 'No history data available')
        this.historyData = []
        this.component?.updateHistory([])
      }
    } catch (error) {
      this.log('error', 'Failed to load history data:', error)
      this.historyData = []
      this.component?.updateHistory([])
    } finally {
      this.isDataLoading = false
    }
  }

  /**
   * 处理历史记录项点击
   */
  private handleHistoryItemClick(itemElement: HTMLElement): void {
    const chatId = itemElement.dataset.chatId
    const chatUid = itemElement.dataset.chatUid
    
    if (!chatUid) {
      this.log('warn', 'No chat UID found for history item')
      return
    }

    // 查找对应的历史数据
    const historyItem = this.historyData.find(item => item.chat_uid === chatUid)
    if (!historyItem) {
      this.log('warn', 'History item not found:', chatUid)
      return
    }

    try {
      // 添加点击效果
      itemElement.classList.add('clicked')
      setTimeout(() => {
        itemElement.classList.remove('clicked')
      }, 300)

      // 注入提示词到当前页面
      this.injectPrompt(historyItem.chat_prompt, chatUid)

      // 隐藏气泡和模态框
      this.component?.hideBubble()
      this.component?.closeModal()

      // 显示成功提示
      this.showToast('提示词已注入', 'success')

      this.log('info', 'History item clicked and prompt injected:', chatUid)
      
    } catch (error) {
      this.log('error', 'Failed to handle history item click:', error)
      this.showToast('注入失败', 'error')
    }
  }

  /**
   * 注入提示词
   */
  private async injectPrompt(prompt: string, chatUid: string): Promise<void> {
    try {
      // 获取输入框元素
      const inputElement = this.adapter.getElement('inputField' as any)
      if (!inputElement) {
        throw new Error('Input element not found')
      }

      // 注入提示词内容
      if (inputElement.tagName === 'INPUT' || inputElement.tagName === 'TEXTAREA') {
        (inputElement as HTMLInputElement).value = prompt
      } else if (inputElement.contentEditable === 'true') {
        inputElement.textContent = prompt
      }

      // 触发输入事件
      inputElement.dispatchEvent(new Event('input', { bubbles: true }))
      inputElement.dispatchEvent(new Event('change', { bubbles: true }))

      // 聚焦到输入框
      inputElement.focus()

      // 发布提示词注入事件
      this.eventBus.publish(BusinessEvent.CONTENT_EXTRACTED, {
        content: prompt,
        type: 'prompt-injection',
        source: inputElement
      })

      this.log('info', 'Prompt injected successfully')
      
    } catch (error) {
      this.log('error', 'Failed to inject prompt:', error)
      throw error
    }
  }

  /**
   * 处理分页点击
   */
  private handlePaginationClick(button: HTMLElement): void {
    if (button.hasAttribute('disabled')) return

    const isPrev = button.textContent === '‹'
    const isNext = button.textContent === '›'

    if (isPrev) {
      // 上一页逻辑会由组件内部处理
    } else if (isNext) {
      // 下一页逻辑会由组件内部处理
    }
  }

  /**
   * 获取平台图标
   */
  private getPlatformIcon(platformName: string): string {
    const iconMap: Record<string, string> = {
      'ChatGPT': '🤖',
      'Claude': '🧠',
      'Gemini': '💎',
      'Copilot': '🚀',
      'Perplexity': '🔍',
      'Character.AI': '🎭',
      'Poe': '📝',
      'You.com': '🌐',
      'Phind': '🔎',
      'Kimi': '🌙'
    }

    return iconMap[platformName] || '💬'
  }

  /**
   * 显示提示消息
   */
  private showToast(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    // 创建简单的toast提示
    const toast = document.createElement('div')
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      font-size: 14px;
      z-index: 10004;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `
    toast.textContent = message

    document.body.appendChild(toast)

    // 显示动画
    requestAnimationFrame(() => {
      toast.style.transform = 'translateX(0)'
    })

    // 自动隐藏
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)'
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast)
        }
      }, 300)
    }, 2000)
  }

  /**
   * 搜索历史记录
   */
  async searchHistory(query: string): Promise<void> {
    if (!this.component) return

    try {
      // 如果有搜索查询，从数据库搜索
      if (query.trim()) {
        const result = await chatHistoryDatabaseProxy.searchChats({
          query: query.trim(),
          limit: this.config.componentConfig?.maxItems || 10
        })

        if (result.success && result.data) {
          const searchResults = result.data.map(item => ({
            id: item.id.toString(),
            chat_uid: item.chat_uid,
            chat_prompt: item.chat_prompt,
            platform_name: item.platform_name,
            create_time: item.create_time,
            platform_icon: this.getPlatformIcon(item.platform_name)
          }))

          this.component.updateHistory(searchResults)
        } else {
          this.component.updateHistory([])
        }
      } else {
        // 空查询时显示所有数据
        this.component.updateHistory(this.historyData)
      }

      this.log('debug', `Search completed for query: ${query}`)
      
    } catch (error) {
      this.log('error', 'Search failed:', error)
    }
  }

  /**
   * 刷新历史数据
   */
  async refreshHistory(): Promise<void> {
    this.lastUpdateTime = 0 // 重置节流
    await this.loadHistoryData()
    
    if (this.component) {
      await this.component.refreshHistory()
    }
  }

  /**
   * 获取历史数据
   */
  getHistoryData(): HistoryItem[] {
    return [...this.historyData]
  }

  /**
   * 取消注入
   */
  protected async onUninject(): Promise<void> {
    // 销毁UI组件
    if (this.component) {
      this.component.destroy()
      this.component = null
    }

    // 清理数据
    this.historyData = []

    this.log('info', 'HistoryBubble uninject completed')
  }

  /**
   * 销毁时清理
   */
  protected onDestroy(): void {
    // 清理历史数据
    this.historyData = []
    this.isDataLoading = false
  }
}
