/**
 * Content模块事件枚举定义
 * 统一管理所有事件类型，确保类型安全和一致性
 */

/**
 * 输入相关事件
 */
export enum InputEvent {
  /** 输入框获得焦点 */
  FOCUSED = 'input-focused',
  /** 输入框内容变化 */
  CHANGED = 'input-changed',
  /** 输入框失去焦点 */
  BLURRED = 'input-blurred',
  /** 输入框被点击 */
  CLICKED = 'input-clicked',
  /** 输入框准备就绪 */
  READY = 'input-ready'
}

/**
 * 页面相关事件
 */
export enum PageEvent {
  /** 页面内容变化 */
  CONTENT_CHANGED = 'page-content-changed',
  /** 页面URL变化 */
  URL_CHANGED = 'page-url-changed',
  /** 页面加载完成 */
  LOADED = 'page-loaded',
  /** 页面DOM变化 */
  DOM_MUTATED = 'page-dom-mutated'
}

/**
 * 元素相关事件
 */
export enum ElementEvent {
  /** 元素被找到 */
  FOUND = 'element-found',
  /** 元素丢失 */
  LOST = 'element-lost',
  /** 元素状态变化 */
  STATE_CHANGED = 'element-state-changed',
  /** 元素位置变化 */
  POSITION_CHANGED = 'element-position-changed'
}

/**
 * UI组件相关事件
 */
export enum UIEvent {
  /** 浮动气泡显示 */
  BUBBLE_SHOW = 'ui-bubble-show',
  /** 浮动气泡隐藏 */
  BUBBLE_HIDE = 'ui-bubble-hide',
  /** 浮动气泡移动 */
  BUBBLE_MOVE = 'ui-bubble-move',
  /** 存档按钮点击 */
  ARCHIVE_CLICK = 'ui-archive-click',
  /** 历史按钮点击 */
  HISTORY_CLICK = 'ui-history-click',
  /** 模态框打开 */
  MODAL_OPEN = 'ui-modal-open',
  /** 模态框关闭 */
  MODAL_CLOSE = 'ui-modal-close'
}

/**
 * 业务逻辑相关事件
 */
export enum BusinessEvent {
  /** 存档请求 */
  ARCHIVE_REQUESTED = 'business-archive-requested',
  /** 存档完成 */
  ARCHIVE_COMPLETED = 'business-archive-completed',
  /** 存档失败 */
  ARCHIVE_FAILED = 'business-archive-failed',
  /** 对话捕获完成 */
  CONVERSATION_CAPTURED = 'business-conversation-captured',
  /** 内容提取完成 */
  CONTENT_EXTRACTED = 'business-content-extracted'
}

/**
 * 适配器相关事件
 */
export enum AdapterEvent {
  /** 适配器初始化完成 */
  INITIALIZED = 'adapter-initialized',
  /** 适配器销毁 */
  DESTROYED = 'adapter-destroyed',
  /** 平台检测完成 */
  PLATFORM_DETECTED = 'adapter-platform-detected',
  /** 配置更新 */
  CONFIG_UPDATED = 'adapter-config-updated'
}

/**
 * 系统相关事件
 */
export enum SystemEvent {
  /** 系统错误 */
  ERROR = 'system-error',
  /** 系统警告 */
  WARNING = 'system-warning',
  /** 系统信息 */
  INFO = 'system-info',
  /** 调试信息 */
  DEBUG = 'system-debug'
}

/**
 * 所有事件类型的联合类型
 */
export type ContentEventType = 
  | InputEvent
  | PageEvent
  | ElementEvent
  | UIEvent
  | BusinessEvent
  | AdapterEvent
  | SystemEvent

/**
 * 事件数据类型映射
 */
export interface ContentEventMap {
  // 输入事件
  [InputEvent.FOCUSED]: { element: HTMLElement; value: string }
  [InputEvent.CHANGED]: { element: HTMLElement; value: string; previousValue?: string }
  [InputEvent.BLURRED]: { element: HTMLElement; value: string }
  [InputEvent.CLICKED]: { element: HTMLElement; position: { x: number; y: number } }
  [InputEvent.READY]: { element: HTMLElement; selector: string }

  // 页面事件
  [PageEvent.CONTENT_CHANGED]: { url: string; mutation?: MutationRecord }
  [PageEvent.URL_CHANGED]: { oldUrl: string; newUrl: string }
  [PageEvent.LOADED]: { url: string; loadTime: number }
  [PageEvent.DOM_MUTATED]: { mutations: MutationRecord[] }

  // 元素事件
  [ElementEvent.FOUND]: { type: string; element: HTMLElement; selector: string }
  [ElementEvent.LOST]: { type: string; selector: string }
  [ElementEvent.STATE_CHANGED]: { element: HTMLElement; oldState: any; newState: any }
  [ElementEvent.POSITION_CHANGED]: { element: HTMLElement; oldPosition: DOMRect; newPosition: DOMRect }

  // UI事件
  [UIEvent.BUBBLE_SHOW]: { position: { x: number; y: number } }
  [UIEvent.BUBBLE_HIDE]: { reason: string }
  [UIEvent.BUBBLE_MOVE]: { from: { x: number; y: number }; to: { x: number; y: number } }
  [UIEvent.ARCHIVE_CLICK]: { content: string; timestamp: number }
  [UIEvent.HISTORY_CLICK]: { timestamp: number }
  [UIEvent.MODAL_OPEN]: { type: string; data?: any }
  [UIEvent.MODAL_CLOSE]: { type: string; result?: any }

  // 业务事件
  [BusinessEvent.ARCHIVE_REQUESTED]: { content: string; promptId: string; metadata?: any }
  [BusinessEvent.ARCHIVE_COMPLETED]: { promptId: string; success: boolean; data?: any }
  [BusinessEvent.ARCHIVE_FAILED]: { promptId: string; error: Error; retryCount?: number }
  [BusinessEvent.CONVERSATION_CAPTURED]: { conversation: any; platform: string }
  [BusinessEvent.CONTENT_EXTRACTED]: { content: string; type: string; source: HTMLElement }

  // 适配器事件
  [AdapterEvent.INITIALIZED]: { platform: string; config: any }
  [AdapterEvent.DESTROYED]: { platform: string; reason?: string }
  [AdapterEvent.PLATFORM_DETECTED]: { platform: string; confidence: number }
  [AdapterEvent.CONFIG_UPDATED]: { platform: string; oldConfig: any; newConfig: any }

  // 系统事件
  [SystemEvent.ERROR]: { error: Error; context?: string; stack?: string }
  [SystemEvent.WARNING]: { message: string; context?: string; data?: any }
  [SystemEvent.INFO]: { message: string; context?: string; data?: any }
  [SystemEvent.DEBUG]: { message: string; context?: string; data?: any }
}

/**
 * 事件优先级枚举
 */
export enum EventPriority {
  /** 低优先级 */
  LOW = 0,
  /** 普通优先级 */
  NORMAL = 1,
  /** 高优先级 */
  HIGH = 2,
  /** 紧急优先级 */
  URGENT = 3
}

/**
 * 事件元数据接口
 */
export interface EventMetadata {
  /** 事件ID */
  id: string
  /** 事件时间戳 */
  timestamp: number
  /** 事件优先级 */
  priority: EventPriority
  /** 事件来源 */
  source?: string
  /** 事件标签 */
  tags?: string[]
  /** 是否可取消 */
  cancelable?: boolean
  /** 是否已取消 */
  cancelled?: boolean
}

/**
 * 完整的事件对象接口
 */
export interface ContentEvent<T = any> {
  /** 事件类型 */
  type: ContentEventType
  /** 事件数据 */
  data: T
  /** 事件元数据 */
  metadata: EventMetadata
}

/**
 * 事件回调函数类型
 */
export type EventCallback<T = any> = (event: ContentEvent<T>) => void | Promise<void>

/**
 * 事件监听器配置
 */
export interface EventListenerConfig {
  /** 是否只监听一次 */
  once?: boolean
  /** 事件优先级过滤 */
  minPriority?: EventPriority
  /** 事件标签过滤 */
  tags?: string[]
  /** 超时时间（毫秒） */
  timeout?: number
}
