/**
 * 工具类统一导出
 * 提供所有工具类的统一入口
 */

// DOM工具类
export { DOMUtils } from './DOMUtils'

// 元素查找器
export { ElementFinder } from './ElementFinder'

// 创建工具类实例的工厂函数
export const createDOMUtils = () => DOMUtils.getInstance()
export const createElementFinder = () => new ElementFinder()

// 工具类接口重新导出
export type { 
  IDOMUtils,
  IElementFinder,
  ElementFinderConfig,
  ElementFindResult,
  PerformanceMetrics,
  IPerformanceOptimizer,
  IStorageManager,
  ILogger,
  IErrorHandler
} from '../types/utils'
