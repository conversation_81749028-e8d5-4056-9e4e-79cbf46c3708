/**
 * 答案提取功能测试脚本
 * 专门测试简化后的答案获取逻辑
 */

class AnswerExtractionTest {
  constructor() {
    this.testResults = []
  }

  /**
   * 运行答案提取测试
   */
  async runTests() {
    console.group('【EchoSync】答案提取功能测试')
    
    try {
      await this.testCopyButtonDetection()
      await this.testAnswerExtraction()
      await this.testClipboardAccess()
      await this.testPlatformSpecificExtraction()
      
      this.printResults()
    } catch (error) {
      console.error('测试执行失败:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 测试复制按钮检测
   */
  async testCopyButtonDetection() {
    console.log('🔍 测试1: 复制按钮检测')
    
    try {
      const copyButtonSelectors = [
        '.ds-icon-button',                    // DeepSeek
        '[aria-label*="copy"]',               // 通用
        '[title*="复制"]',                    // 中文
        '[title*="Copy"]',                    // 英文
        '[data-testid*="copy"]',              // 测试ID
        'button[class*="copy"]',              // 包含copy的按钮
        '.copy-button',                       // 通用复制按钮类
        '[role="button"][aria-label*="Copy"]' // 可访问性标准
      ]
      
      let foundButtons = 0
      const buttonInfo = []
      
      for (const selector of copyButtonSelectors) {
        const buttons = document.querySelectorAll(selector)
        if (buttons.length > 0) {
          foundButtons += buttons.length
          buttonInfo.push(`${selector}: ${buttons.length}个`)
        }
      }
      
      if (foundButtons > 0) {
        this.addTestResult('复制按钮检测', true, `找到${foundButtons}个复制按钮: ${buttonInfo.join(', ')}`)
        console.log('✅ 复制按钮检测成功')
      } else {
        this.addTestResult('复制按钮检测', false, '未找到任何复制按钮')
      }
    } catch (error) {
      this.addTestResult('复制按钮检测', false, '测试异常: ' + error.message)
    }
  }

  /**
   * 测试答案内容提取
   */
  async testAnswerExtraction() {
    console.log('🔍 测试2: 答案内容提取')
    
    try {
      const answerSelectors = [
        '.ds-markdown',           // DeepSeek
        '.markdown',              // ChatGPT
        '.font-claude-message',   // Claude
        '.message-content',       // 通用
        '.assistant-message',     // 通用
        '.segment-content'        // Kimi
      ]
      
      let foundAnswers = 0
      const answerInfo = []
      
      for (const selector of answerSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          foundAnswers += elements.length
          // 获取最后一个元素的内容预览
          const lastElement = elements[elements.length - 1]
          const content = lastElement.textContent?.trim() || ''
          const preview = content.length > 50 ? content.substring(0, 50) + '...' : content
          answerInfo.push(`${selector}: ${elements.length}个 (最新: "${preview}")`)
        }
      }
      
      if (foundAnswers > 0) {
        this.addTestResult('答案内容提取', true, `找到${foundAnswers}个答案元素: ${answerInfo.join('; ')}`)
        console.log('✅ 答案内容提取成功')
      } else {
        this.addTestResult('答案内容提取', false, '未找到任何答案元素')
      }
    } catch (error) {
      this.addTestResult('答案内容提取', false, '测试异常: ' + error.message)
    }
  }

  /**
   * 测试剪贴板访问
   */
  async testClipboardAccess() {
    console.log('🔍 测试3: 剪贴板访问')
    
    try {
      if (navigator.clipboard && navigator.clipboard.readText) {
        // 测试剪贴板读取权限
        try {
          await navigator.clipboard.readText()
          this.addTestResult('剪贴板访问', true, '剪贴板API可用')
          console.log('✅ 剪贴板访问正常')
        } catch (error) {
          if (error.name === 'NotAllowedError') {
            this.addTestResult('剪贴板访问', false, '剪贴板权限被拒绝')
          } else {
            this.addTestResult('剪贴板访问', true, '剪贴板API可用（权限待授予）')
          }
        }
      } else {
        this.addTestResult('剪贴板访问', false, '浏览器不支持剪贴板API')
      }
    } catch (error) {
      this.addTestResult('剪贴板访问', false, '测试异常: ' + error.message)
    }
  }

  /**
   * 测试平台特定的答案提取
   */
  async testPlatformSpecificExtraction() {
    console.log('🔍 测试4: 平台特定答案提取')
    
    try {
      const currentDomain = window.location.hostname
      let platformDetected = false
      let extractionResult = null
      
      // 根据当前域名测试对应平台的提取逻辑
      if (currentDomain === 'chat.deepseek.com') {
        platformDetected = true
        extractionResult = await this.testDeepSeekExtraction()
      } else if (currentDomain === 'chat.openai.com') {
        platformDetected = true
        extractionResult = await this.testChatGPTExtraction()
      } else if (currentDomain === 'claude.ai') {
        platformDetected = true
        extractionResult = await this.testClaudeExtraction()
      } else if (currentDomain === 'gemini.google.com') {
        platformDetected = true
        extractionResult = await this.testGeminiExtraction()
      } else if (currentDomain === 'kimi.moonshot.cn') {
        platformDetected = true
        extractionResult = await this.testKimiExtraction()
      }
      
      if (platformDetected) {
        if (extractionResult) {
          this.addTestResult('平台特定提取', true, `${currentDomain} 平台提取成功: ${extractionResult.substring(0, 100)}...`)
          console.log('✅ 平台特定答案提取成功')
        } else {
          this.addTestResult('平台特定提取', false, `${currentDomain} 平台未找到答案内容`)
        }
      } else {
        this.addTestResult('平台特定提取', false, `未识别的平台: ${currentDomain}`)
      }
    } catch (error) {
      this.addTestResult('平台特定提取', false, '测试异常: ' + error.message)
    }
  }

  /**
   * 测试 DeepSeek 答案提取
   */
  async testDeepSeekExtraction() {
    const answerElements = document.querySelectorAll('.ds-markdown')
    if (answerElements.length === 0) return null
    
    const latestAnswer = answerElements[answerElements.length - 1]
    return latestAnswer.textContent?.trim() || null
  }

  /**
   * 测试 ChatGPT 答案提取
   */
  async testChatGPTExtraction() {
    const answerElements = document.querySelectorAll('.markdown, .prose, [data-message-content]')
    if (answerElements.length === 0) return null
    
    for (let i = answerElements.length - 1; i >= 0; i--) {
      const element = answerElements[i]
      const isUserMessage = element.closest('[data-message-author-role="user"]') !== null
      
      if (!isUserMessage) {
        const content = element.textContent?.trim() || ''
        if (content.length > 0) return content
      }
    }
    
    return null
  }

  /**
   * 测试 Claude 答案提取
   */
  async testClaudeExtraction() {
    const answerElements = document.querySelectorAll('.font-claude-message, [data-testid="message-content"]')
    if (answerElements.length === 0) return null
    
    for (let i = answerElements.length - 1; i >= 0; i--) {
      const element = answerElements[i]
      const isUserMessage = element.closest('[data-testid="user-message"]') !== null
      
      if (!isUserMessage) {
        const content = element.textContent?.trim() || ''
        if (content.length > 0) return content
      }
    }
    
    return null
  }

  /**
   * 测试 Gemini 答案提取
   */
  async testGeminiExtraction() {
    const answerElements = document.querySelectorAll('[data-test-id="message-content"], .message-content, .markdown-content')
    if (answerElements.length === 0) return null
    
    for (let i = answerElements.length - 1; i >= 0; i--) {
      const element = answerElements[i]
      const isUserMessage = element.closest('[data-test-id="user-message"]') !== null
      
      if (!isUserMessage) {
        const content = element.textContent?.trim() || ''
        if (content.length > 0) return content
      }
    }
    
    return null
  }

  /**
   * 测试 Kimi 答案提取
   */
  async testKimiExtraction() {
    const answerElements = document.querySelectorAll('.message-content, .content, .segment-content')
    if (answerElements.length === 0) return null
    
    for (let i = answerElements.length - 1; i >= 0; i--) {
      const element = answerElements[i]
      const isUserMessage = element.closest('[data-role="user"]') !== null ||
                           element.closest('.user-message') !== null
      
      if (!isUserMessage) {
        const content = element.textContent?.trim() || ''
        if (content.length > 0) return content
      }
    }
    
    return null
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, passed, message) {
    this.testResults.push({
      name: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 打印测试结果
   */
  printResults() {
    console.group('📊 答案提取测试结果')
    
    const passedTests = this.testResults.filter(r => r.passed).length
    const totalTests = this.testResults.length
    
    console.log(`总测试数: ${totalTests}`)
    console.log(`通过测试: ${passedTests}`)
    console.log(`失败测试: ${totalTests - passedTests}`)
    console.log(`通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)
    
    console.table(this.testResults)
    
    console.groupEnd()
  }
}

// 导出测试类
window.AnswerExtractionTest = AnswerExtractionTest

// 使用说明
console.log(`
【EchoSync】答案提取功能测试脚本已加载

使用方法:
const test = new AnswerExtractionTest()
test.runTests()

建议在有AI对话内容的页面上运行以获得最准确的测试结果
`)
