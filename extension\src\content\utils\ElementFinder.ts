/**
 * 元素查找器
 * 提供多种查找策略和缓存机制的高级元素查找功能
 */

import { 
  IElementFinder, 
  ElementFinderConfig, 
  ElementFindResult 
} from '../types/utils'
import { DOMUtils } from './DOMUtils'

/**
 * 查找策略枚举
 */
enum FindStrategy {
  /** 标准CSS选择器 */
  CSS_SELECTOR = 'css_selector',
  /** XPath查找 */
  XPATH = 'xpath',
  /** 文本内容匹配 */
  TEXT_CONTENT = 'text_content',
  /** 属性匹配 */
  ATTRIBUTE_MATCH = 'attribute_match',
  /** 模糊匹配 */
  FUZZY_MATCH = 'fuzzy_match'
}

/**
 * 缓存条目接口
 */
interface CacheEntry {
  element: HTMLElement
  selector: string
  timestamp: number
  hitCount: number
}

/**
 * 元素查找器实现
 */
export class ElementFinder implements IElementFinder {
  private domUtils: DOMUtils
  private cache: Map<string, CacheEntry> = new Map()
  private cacheStats = {
    hits: 0,
    misses: 0,
    totalQueries: 0
  }

  private defaultConfig: ElementFinderConfig = {
    timeout: 5000,
    enableCache: true,
    cacheExpiry: 30000, // 30秒
    debug: false,
    retryCount: 3,
    retryInterval: 500
  }

  constructor() {
    this.domUtils = DOMUtils.getInstance()
    
    // 定期清理过期缓存
    setInterval(() => {
      this.cleanExpiredCache()
    }, 60000) // 每分钟清理一次
  }

  /**
   * 查找单个元素
   */
  async findElement(
    selectors: string[], 
    config?: ElementFinderConfig
  ): Promise<ElementFindResult> {
    const finalConfig = { ...this.defaultConfig, ...config }
    const startTime = performance.now()
    
    this.cacheStats.totalQueries++

    // 尝试从缓存获取
    if (finalConfig.enableCache) {
      const cachedResult = this.getCachedElement(selectors)
      if (cachedResult) {
        this.cacheStats.hits++
        return {
          ...cachedResult,
          findTime: performance.now() - startTime,
          fromCache: true
        }
      }
    }

    this.cacheStats.misses++

    // 执行查找
    const result = await this.executeFind(selectors, finalConfig, startTime)
    
    // 缓存结果
    if (finalConfig.enableCache && result.element) {
      this.cacheElement(result.selector, result.element)
    }

    return result
  }

  /**
   * 查找多个元素
   */
  async findElements(
    selectors: string[], 
    config?: ElementFinderConfig
  ): Promise<HTMLElement[]> {
    const finalConfig = { ...this.defaultConfig, ...config }
    const elements: HTMLElement[] = []

    for (const selector of selectors) {
      try {
        const foundElements = document.querySelectorAll(selector) as NodeListOf<HTMLElement>
        elements.push(...Array.from(foundElements))
      } catch (error) {
        if (finalConfig.debug) {
          console.warn(`[ElementFinder] Invalid selector: ${selector}`, error)
        }
      }
    }

    return elements
  }

  /**
   * 查找元素（同步）
   */
  findElementSync(selectors: string[]): HTMLElement | null {
    // 先尝试缓存
    const cachedResult = this.getCachedElement(selectors)
    if (cachedResult) {
      return cachedResult.element
    }

    // 同步查找
    for (let i = 0; i < selectors.length; i++) {
      const selector = selectors[i]
      try {
        const element = document.querySelector(selector) as HTMLElement
        if (element && this.domUtils.isElementVisible(element)) {
          // 缓存结果
          this.cacheElement(selector, element)
          return element
        }
      } catch (error) {
        console.warn(`[ElementFinder] Invalid selector: ${selector}`, error)
      }
    }

    return null
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
    this.cacheStats = {
      hits: 0,
      misses: 0,
      totalQueries: 0
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number; hitRate: number; missRate: number } {
    const { hits, misses, totalQueries } = this.cacheStats
    
    return {
      size: this.cache.size,
      hitRate: totalQueries > 0 ? (hits / totalQueries) * 100 : 0,
      missRate: totalQueries > 0 ? (misses / totalQueries) * 100 : 0
    }
  }

  /**
   * 执行查找
   */
  private async executeFind(
    selectors: string[], 
    config: ElementFinderConfig, 
    startTime: number
  ): Promise<ElementFindResult> {
    let lastError: Error | null = null

    // 重试机制
    for (let attempt = 0; attempt <= (config.retryCount || 0); attempt++) {
      try {
        // 尝试不同的查找策略
        const result = await this.tryFindStrategies(selectors, config)
        if (result) {
          return {
            ...result,
            findTime: performance.now() - startTime,
            fromCache: false
          }
        }
      } catch (error) {
        lastError = error as Error
        
        if (config.debug) {
          console.warn(`[ElementFinder] Find attempt ${attempt + 1} failed:`, error)
        }
        
        // 如果不是最后一次尝试，等待重试间隔
        if (attempt < (config.retryCount || 0)) {
          await this.delay(config.retryInterval || 500)
        }
      }
    }

    // 所有尝试都失败了
    return {
      element: null,
      selector: selectors[0] || '',
      selectorIndex: -1,
      findTime: performance.now() - startTime,
      fromCache: false
    }
  }

  /**
   * 尝试不同的查找策略
   */
  private async tryFindStrategies(
    selectors: string[], 
    config: ElementFinderConfig
  ): Promise<{ element: HTMLElement; selector: string; selectorIndex: number } | null> {
    // 策略1: 直接CSS选择器查找
    const directResult = this.tryDirectFind(selectors)
    if (directResult) {
      return directResult
    }

    // 策略2: 等待元素出现
    if (config.timeout && config.timeout > 0) {
      const waitResult = await this.tryWaitFind(selectors, config.timeout)
      if (waitResult) {
        return waitResult
      }
    }

    // 策略3: 模糊匹配
    const fuzzyResult = this.tryFuzzyFind(selectors)
    if (fuzzyResult) {
      return fuzzyResult
    }

    // 策略4: 文本内容匹配
    const textResult = this.tryTextContentFind(selectors)
    if (textResult) {
      return textResult
    }

    return null
  }

  /**
   * 直接查找策略
   */
  private tryDirectFind(
    selectors: string[]
  ): { element: HTMLElement; selector: string; selectorIndex: number } | null {
    for (let i = 0; i < selectors.length; i++) {
      const selector = selectors[i]
      try {
        const element = document.querySelector(selector) as HTMLElement
        if (element && this.domUtils.isElementVisible(element)) {
          return { element, selector, selectorIndex: i }
        }
      } catch (error) {
        // 忽略无效选择器错误
      }
    }
    return null
  }

  /**
   * 等待查找策略
   */
  private async tryWaitFind(
    selectors: string[], 
    timeout: number
  ): Promise<{ element: HTMLElement; selector: string; selectorIndex: number } | null> {
    for (let i = 0; i < selectors.length; i++) {
      const selector = selectors[i]
      try {
        const element = await this.domUtils.waitForElement(selector, timeout)
        if (element && this.domUtils.isElementVisible(element)) {
          return { element, selector, selectorIndex: i }
        }
      } catch (error) {
        // 继续尝试下一个选择器
      }
    }
    return null
  }

  /**
   * 模糊匹配策略
   */
  private tryFuzzyFind(
    selectors: string[]
  ): { element: HTMLElement; selector: string; selectorIndex: number } | null {
    for (let i = 0; i < selectors.length; i++) {
      const selector = selectors[i]
      
      // 尝试部分匹配
      const fuzzySelectors = this.generateFuzzySelectors(selector)
      
      for (const fuzzySelector of fuzzySelectors) {
        try {
          const element = document.querySelector(fuzzySelector) as HTMLElement
          if (element && this.domUtils.isElementVisible(element)) {
            return { element, selector: fuzzySelector, selectorIndex: i }
          }
        } catch (error) {
          // 继续尝试
        }
      }
    }
    return null
  }

  /**
   * 文本内容匹配策略
   */
  private tryTextContentFind(
    selectors: string[]
  ): { element: HTMLElement; selector: string; selectorIndex: number } | null {
    // 这里可以实现基于文本内容的查找逻辑
    // 暂时返回null，可以根据需要扩展
    return null
  }

  /**
   * 生成模糊选择器
   */
  private generateFuzzySelectors(selector: string): string[] {
    const fuzzySelectors: string[] = []
    
    // 移除伪类选择器
    const withoutPseudo = selector.replace(/:[\w-]+(\([^)]*\))?/g, '')
    if (withoutPseudo !== selector) {
      fuzzySelectors.push(withoutPseudo)
    }
    
    // 移除属性选择器
    const withoutAttributes = selector.replace(/\[[^\]]*\]/g, '')
    if (withoutAttributes !== selector) {
      fuzzySelectors.push(withoutAttributes)
    }
    
    // 只保留标签名和类名
    const simplified = selector.replace(/[#\[\]:]/g, '').split(' ')[0]
    if (simplified && simplified !== selector) {
      fuzzySelectors.push(simplified)
    }
    
    return fuzzySelectors
  }

  /**
   * 从缓存获取元素
   */
  private getCachedElement(selectors: string[]): ElementFindResult | null {
    for (const selector of selectors) {
      const cacheKey = this.getCacheKey(selector)
      const cached = this.cache.get(cacheKey)
      
      if (cached && this.isCacheValid(cached)) {
        // 验证元素是否仍然存在且可见
        if (document.contains(cached.element) && this.domUtils.isElementVisible(cached.element)) {
          cached.hitCount++
          return {
            element: cached.element,
            selector: cached.selector,
            selectorIndex: 0,
            findTime: 0,
            fromCache: true
          }
        } else {
          // 元素已失效，从缓存中移除
          this.cache.delete(cacheKey)
        }
      }
    }
    
    return null
  }

  /**
   * 缓存元素
   */
  private cacheElement(selector: string, element: HTMLElement): void {
    const cacheKey = this.getCacheKey(selector)
    
    this.cache.set(cacheKey, {
      element,
      selector,
      timestamp: Date.now(),
      hitCount: 0
    })
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cached: CacheEntry): boolean {
    const now = Date.now()
    return (now - cached.timestamp) < (this.defaultConfig.cacheExpiry || 30000)
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now()
    const expiry = this.defaultConfig.cacheExpiry || 30000
    
    for (const [key, cached] of this.cache.entries()) {
      if ((now - cached.timestamp) > expiry) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 获取缓存键
   */
  private getCacheKey(selector: string): string {
    return `selector_${selector}`
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
