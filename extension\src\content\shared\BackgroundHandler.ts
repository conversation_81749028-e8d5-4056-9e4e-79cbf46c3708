import { MessagingService } from '@/lib/service/messagingService'
import { MessageType, ChromeMessage } from '@/types'
import { BaseAIAdapter } from '../base/BaseAIAdapter'

/**
 * 消息处理器
 * 独立处理与background的消息通信，将消息路由到适配器方法
 */
export class BackgroundHandler {
  private adapter: BaseAIAdapter | null = null
  private isListening: boolean = false

  constructor() {
    this.setupMessageListener()
  }

  /**
   * 设置适配器
   */
  setAdapter(adapter: BaseAIAdapter): void {
    this.adapter = adapter
    console.log('【BackgroundHandler】Adapter set:', adapter.getPlatformName())
  }

  /**
   * 设置消息监听器
   */
  private setupMessageListener(): void {
    if (this.isListening) {
      console.warn('【BackgroundHandler】Message listener already setup')
      return
    }

    MessagingService.onMessage(async (message, sender, sendResponse) => {
      try {
        console.log('【BackgroundHandler】Received message:', message.type, message.payload)
        
        if (!this.adapter) {
          console.warn('【BackgroundHandler】No adapter available for message:', message.type)
          sendResponse({
            success: false,
            error: 'No adapter available'
          })
          return
        }

        const result = await this.handleMessage(message)
        sendResponse(result)
        
      } catch (error) {
        console.error('【BackgroundHandler】Error handling message:', error)
        sendResponse({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    })

    this.isListening = true
    console.log('【BackgroundHandler】Message listener setup complete')
  }

  /**
   * 处理消息
   */
  private async handleMessage(message: ChromeMessage): Promise<any> {
    if (!this.adapter) {
      throw new Error('No adapter available')
    }

    switch (message.type) {
      case MessageType.INJECT_PROMPT:
        return await this.handleInjectPrompt(message.payload)

      case MessageType.CAPTURE_PROMPT:
        return await this.handleCapturePrompt()

      case MessageType.EXTRACT_CONVERSATION:
        return await this.handleExtractConversation()

      case MessageType.CHECK_PAGE_VALIDITY:
        return await this.handleCheckPageValidity()

      case MessageType.GET_PLATFORM_INFO:
        return await this.handleGetPlatformInfo()

      default:
        throw new Error(`Unknown message type: ${message.type}`)
    }
  }

  /**
   * 处理注入提示词消息
   */
  private async handleInjectPrompt(payload: any): Promise<any> {
    if (!payload.prompt) {
      throw new Error('Prompt is required')
    }

    await this.adapter!.injectPrompt(payload.prompt)
    
    console.log('【BackgroundHandler】Prompt injected successfully')
    return {
      success: true,
      data: {
        platform: this.adapter!.getPlatformName(),
        prompt: payload.prompt
      }
    }
  }

  /**
   * 处理捕获提示词消息
   */
  private async handleCapturePrompt(): Promise<any> {
    const prompt = this.adapter!.getCurrentInput()
    
    if (prompt) {
      // 发送到background进行处理
      await MessagingService.sendToBackground(MessageType.CAPTURE_PROMPT, {
        content: prompt,
        platform: this.adapter!.getPlatformName().toLowerCase()
      })
    }

    console.log('【BackgroundHandler】Prompt captured:', prompt ? 'success' : 'empty')
    return {
      success: true,
      data: {
        prompt,
        platform: this.adapter!.getPlatformName(),
        isEmpty: !prompt
      }
    }
  }

  /**
   * 处理提取对话消息
   */
  private async handleExtractConversation(): Promise<any> {
    const conversation = await this.adapter!.extractConversation()
    
    console.log('【BackgroundHandler】Conversation extracted:', conversation ? 'success' : 'failed')
    return {
      success: true,
      data: {
        conversation,
        platform: this.adapter!.getPlatformName(),
        hasConversation: !!conversation
      }
    }
  }

  /**
   * 处理检查页面有效性消息
   */
  private async handleCheckPageValidity(): Promise<any> {
    const isValid = this.adapter!.isValidPage()
    
    console.log('【BackgroundHandler】Page validity checked:', isValid)
    return {
      success: true,
      data: {
        isValid,
        platform: this.adapter!.getPlatformName(),
        url: window.location.href
      }
    }
  }

  /**
   * 处理获取平台信息消息
   */
  private async handleGetPlatformInfo(): Promise<any> {
    const config = this.adapter!.getConfig()
    
    console.log('【BackgroundHandler】Platform info retrieved')
    return {
      success: true,
      data: {
        name: config.name,
        id: config.id,
        url: config.url,
        currentUrl: window.location.href,
        isValidPage: this.adapter!.isValidPage()
      }
    }
  }

  /**
   * 发送消息到background
   */
  async sendToBackground(type: MessageType, payload?: any): Promise<any> {
    try {
      const response = await MessagingService.sendToBackground(type, payload)
      console.log('【BackgroundHandler】Message sent to background:', type, response)
      return response
    } catch (error) {
      console.error('【BackgroundHandler】Error sending message to background:', error)
      throw error
    }
  }

  /**
   * 发送通知消息
   */
  async sendNotification(message: string, type: 'info' | 'success' | 'error' = 'info'): Promise<void> {
    try {
      await this.sendToBackground(MessageType.SHOW_NOTIFICATION, {
        message,
        type,
        platform: this.adapter?.getPlatformName() || 'Unknown'
      })
    } catch (error) {
      console.error('【BackgroundHandler】Error sending notification:', error)
    }
  }

  /**
   * 获取适配器状态
   */
  getAdapterStatus(): {
    hasAdapter: boolean
    platformName: string | null
    isValidPage: boolean | null
  } {
    return {
      hasAdapter: !!this.adapter,
      platformName: this.adapter?.getPlatformName() || null,
      isValidPage: this.adapter?.isValidPage() || null
    }
  }

  /**
   * 销毁消息处理器
   */
  destroy(): void {
    this.adapter = null
    this.isListening = false
    console.log('【BackgroundHandler】Message handler destroyed')
  }
}
