/**
 * EventBus 单元测试
 */

import { EventBus } from '../EventBus'
import { InputEvent, PageEvent, EventPriority } from '../EventEnum'

describe('EventBus', () => {
  let eventBus: EventBus

  beforeEach(() => {
    eventBus = new EventBus({
      debugMode: false,
      maxListeners: 10,
      performanceMonitoring: true
    })
  })

  afterEach(() => {
    eventBus.clear()
  })

  describe('基本功能', () => {
    test('应该能够订阅和发布事件', () => {
      const mockCallback = jest.fn()
      
      eventBus.subscribe(InputEvent.FOCUSED, mockCallback)
      eventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test'
      })

      expect(mockCallback).toHaveBeenCalledTimes(1)
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          type: InputEvent.FOCUSED,
          data: expect.objectContaining({
            element: expect.any(HTMLElement),
            value: 'test'
          })
        })
      )
    })

    test('应该能够取消订阅事件', () => {
      const mockCallback = jest.fn()
      
      const unsubscribe = eventBus.subscribe(InputEvent.FOCUSED, mockCallback)
      unsubscribe()
      
      eventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test'
      })

      expect(mockCallback).not.toHaveBeenCalled()
    })

    test('应该支持一次性监听器', () => {
      const mockCallback = jest.fn()
      
      eventBus.once(InputEvent.FOCUSED, mockCallback)
      
      // 第一次发布
      eventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test1'
      })
      
      // 第二次发布
      eventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test2'
      })

      expect(mockCallback).toHaveBeenCalledTimes(1)
    })
  })

  describe('错误处理', () => {
    test('应该捕获监听器中的错误', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const errorCallback = jest.fn(() => {
        throw new Error('Test error')
      })
      const normalCallback = jest.fn()
      
      eventBus.subscribe(InputEvent.FOCUSED, errorCallback)
      eventBus.subscribe(InputEvent.FOCUSED, normalCallback)
      
      eventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test'
      })

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error in listener'),
        expect.any(Error)
      )
      expect(normalCallback).toHaveBeenCalled()
      
      consoleSpy.mockRestore()
    })

    test('应该限制最大监听器数量', () => {
      const eventBusSmall = new EventBus({ maxListeners: 2 })
      
      eventBusSmall.subscribe(InputEvent.FOCUSED, jest.fn())
      eventBusSmall.subscribe(InputEvent.FOCUSED, jest.fn())
      
      expect(() => {
        eventBusSmall.subscribe(InputEvent.FOCUSED, jest.fn())
      }).toThrow('Maximum listeners')
    })
  })

  describe('事件优先级', () => {
    test('应该按优先级执行监听器', () => {
      const executionOrder: number[] = []
      
      const lowPriorityCallback = jest.fn(() => executionOrder.push(1))
      const highPriorityCallback = jest.fn(() => executionOrder.push(2))
      
      eventBus.subscribe(InputEvent.FOCUSED, lowPriorityCallback, {
        minPriority: EventPriority.LOW
      })
      eventBus.subscribe(InputEvent.FOCUSED, highPriorityCallback, {
        minPriority: EventPriority.HIGH
      })
      
      eventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test'
      }, {
        priority: EventPriority.HIGH
      })

      expect(executionOrder).toEqual([2, 1])
    })

    test('应该过滤低优先级监听器', () => {
      const lowPriorityCallback = jest.fn()
      const highPriorityCallback = jest.fn()
      
      eventBus.subscribe(InputEvent.FOCUSED, lowPriorityCallback, {
        minPriority: EventPriority.HIGH
      })
      eventBus.subscribe(InputEvent.FOCUSED, highPriorityCallback, {
        minPriority: EventPriority.LOW
      })
      
      eventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test'
      }, {
        priority: EventPriority.NORMAL
      })

      expect(lowPriorityCallback).not.toHaveBeenCalled()
      expect(highPriorityCallback).toHaveBeenCalled()
    })
  })

  describe('标签过滤', () => {
    test('应该根据标签过滤监听器', () => {
      const taggedCallback = jest.fn()
      const untaggedCallback = jest.fn()
      
      eventBus.subscribe(InputEvent.FOCUSED, taggedCallback, {
        tags: ['ui', 'input']
      })
      eventBus.subscribe(InputEvent.FOCUSED, untaggedCallback)
      
      eventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test'
      }, {
        tags: ['ui']
      })

      expect(taggedCallback).toHaveBeenCalled()
      expect(untaggedCallback).toHaveBeenCalled()
    })
  })

  describe('统计信息', () => {
    test('应该正确统计监听器数量', () => {
      eventBus.subscribe(InputEvent.FOCUSED, jest.fn())
      eventBus.subscribe(InputEvent.FOCUSED, jest.fn())
      eventBus.subscribe(PageEvent.LOADED, jest.fn())

      expect(eventBus.getListenerCount(InputEvent.FOCUSED)).toBe(2)
      expect(eventBus.getListenerCount(PageEvent.LOADED)).toBe(1)
      expect(eventBus.getListenerCount(InputEvent.CHANGED)).toBe(0)
    })

    test('应该正确统计事件发布次数', () => {
      eventBus.subscribe(InputEvent.FOCUSED, jest.fn())
      
      eventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test1'
      })
      eventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test2'
      })

      const stats = eventBus.getEventStats(InputEvent.FOCUSED)
      expect(stats?.publishCount).toBe(2)
    })

    test('应该获取所有事件名称', () => {
      eventBus.subscribe(InputEvent.FOCUSED, jest.fn())
      eventBus.subscribe(PageEvent.LOADED, jest.fn())

      const eventNames = eventBus.getEventNames()
      expect(eventNames).toContain(InputEvent.FOCUSED)
      expect(eventNames).toContain(PageEvent.LOADED)
      expect(eventNames).toHaveLength(2)
    })
  })

  describe('清理功能', () => {
    test('应该能够清除所有监听器', () => {
      eventBus.subscribe(InputEvent.FOCUSED, jest.fn())
      eventBus.subscribe(PageEvent.LOADED, jest.fn())

      expect(eventBus.getEventNames()).toHaveLength(2)
      
      eventBus.clear()
      
      expect(eventBus.getEventNames()).toHaveLength(0)
    })

    test('应该能够清除指定事件的监听器', () => {
      eventBus.subscribe(InputEvent.FOCUSED, jest.fn())
      eventBus.subscribe(PageEvent.LOADED, jest.fn())

      expect(eventBus.getEventNames()).toHaveLength(2)
      
      eventBus.clearEvent(InputEvent.FOCUSED)
      
      expect(eventBus.getEventNames()).toHaveLength(1)
      expect(eventBus.getEventNames()).toContain(PageEvent.LOADED)
    })
  })

  describe('配置选项', () => {
    test('应该支持调试模式', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
      const debugEventBus = new EventBus({ debugMode: true })
      
      debugEventBus.subscribe(InputEvent.FOCUSED, jest.fn())
      debugEventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test'
      })

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Subscribed to event')
      )
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Publishing event')
      )
      
      consoleSpy.mockRestore()
    })

    test('应该支持性能监控', () => {
      const performanceEventBus = new EventBus({ performanceMonitoring: true })
      
      performanceEventBus.subscribe(InputEvent.FOCUSED, jest.fn())
      performanceEventBus.publish(InputEvent.FOCUSED, {
        element: document.createElement('input'),
        value: 'test'
      })

      const stats = performanceEventBus.getEventStats(InputEvent.FOCUSED)
      expect(stats?.averageExecutionTime).toBeGreaterThanOrEqual(0)
    })
  })
})
