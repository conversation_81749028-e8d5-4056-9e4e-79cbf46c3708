/**
 * Content模块类型定义统一导出
 * 提供所有类型定义的统一入口
 */

// 事件相关类型
export * from './events'

// 组件相关类型
export * from './components'

// 适配器相关类型
export * from './adapters'

// 工具类相关类型
export * from './utils'

// 平台配置相关类型（保持向后兼容）
export * from './PlatformConfig'

// 重新导出共享的事件枚举
export * from '../shared/EventEnum'

/**
 * 常用类型别名
 */

// 事件相关
export type { 
  ContentEvent,
  EventCallback,
  EventMetadata,
  EventListenerConfig,
  IEventBus
} from './events'

// 组件相关
export type {
  IComponent,
  IInject,
  IFloatingBubble,
  IArchiveButton,
  IHistoryBubble,
  ComponentConfig,
  InjectConfig
} from './components'

// 适配器相关
export type {
  IAIAdapter,
  IBaseAdapter,
  PlatformConfig,
  SelectorConfig,
  AdapterState,
  ElementType
} from './adapters'

// 工具类相关
export type {
  IDOMUtils,
  IElementFinder,
  IPerformanceOptimizer,
  IStorageManager,
  ILogger,
  IErrorHandler,
  Result,
  BaseConfig
} from './utils'

/**
 * 常用枚举重新导出
 */
export {
  ComponentLifecycleState,
  ElementType,
  AdapterState,
  AdapterErrorType,
  LogLevel
} from './adapters'

export {
  EventPriority,
  InputEvent,
  PageEvent,
  ElementEvent,
  UIEvent,
  BusinessEvent,
  AdapterEvent,
  SystemEvent
} from '../shared/EventEnum'

/**
 * 全局类型声明
 */
declare global {
  interface Window {
    /** EchoSync全局对象 */
    EchoSync?: {
      /** 当前适配器实例 */
      currentAdapter?: IAIAdapter
      /** 事件总线实例 */
      eventBus?: IEventBus
      /** 调试模式 */
      debug?: boolean
      /** 版本信息 */
      version?: string
    }
  }
}

/**
 * 模块声明
 */
declare module '*.css' {
  const content: string
  export default content
}

declare module '*.scss' {
  const content: string
  export default content
}

declare module '*.less' {
  const content: string
  export default content
}

/**
 * 类型守卫函数
 */

/**
 * 检查是否为有效的HTML元素
 */
export function isHTMLElement(element: any): element is HTMLElement {
  return element instanceof HTMLElement
}

/**
 * 检查是否为有效的事件对象
 */
export function isContentEvent(event: any): event is ContentEvent {
  return (
    typeof event === 'object' &&
    event !== null &&
    typeof event.type === 'string' &&
    'data' in event &&
    'metadata' in event
  )
}

/**
 * 检查是否为有效的组件
 */
export function isComponent(component: any): component is IComponent {
  return (
    typeof component === 'object' &&
    component !== null &&
    typeof component.id === 'string' &&
    typeof component.name === 'string' &&
    typeof component.render === 'function' &&
    typeof component.destroy === 'function'
  )
}

/**
 * 检查是否为有效的注入器
 */
export function isInject(inject: any): inject is IInject {
  return (
    typeof inject === 'object' &&
    inject !== null &&
    typeof inject.id === 'string' &&
    typeof inject.name === 'string' &&
    typeof inject.inject === 'function' &&
    typeof inject.destroy === 'function'
  )
}

/**
 * 检查是否为有效的适配器
 */
export function isAdapter(adapter: any): adapter is IAIAdapter {
  return (
    typeof adapter === 'object' &&
    adapter !== null &&
    typeof adapter.id === 'string' &&
    typeof adapter.name === 'string' &&
    typeof adapter.initialize === 'function' &&
    typeof adapter.destroy === 'function' &&
    typeof adapter.extractConversation === 'function'
  )
}

/**
 * 检查是否为有效的结果对象
 */
export function isResult<T>(result: any): result is Result<T> {
  return (
    typeof result === 'object' &&
    result !== null &&
    typeof result.success === 'boolean' &&
    typeof result.timestamp === 'number'
  )
}

/**
 * 工具类型
 */

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * 提取函数参数类型
 */
export type ExtractFunctionArgs<T> = T extends (...args: infer A) => any ? A : never

/**
 * 提取函数返回类型
 */
export type ExtractFunctionReturn<T> = T extends (...args: any[]) => infer R ? R : never

/**
 * 提取Promise类型
 */
export type ExtractPromiseType<T> = T extends Promise<infer U> ? U : T

/**
 * 联合类型转交集类型
 */
export type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (
  k: infer I
) => void
  ? I
  : never

/**
 * 获取对象的值类型
 */
export type ValueOf<T> = T[keyof T]

/**
 * 获取数组元素类型
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never

/**
 * 条件类型
 */
export type If<C extends boolean, T, F> = C extends true ? T : F

/**
 * 非空类型
 */
export type NonNullable<T> = T extends null | undefined ? never : T

/**
 * 可空类型
 */
export type Nullable<T> = T | null

/**
 * 可选类型
 */
export type Optional<T> = T | undefined

/**
 * 字符串字面量类型
 */
export type StringLiteral<T> = T extends string ? (string extends T ? never : T) : never

/**
 * 数字字面量类型
 */
export type NumberLiteral<T> = T extends number ? (number extends T ? never : T) : never

/**
 * 布尔字面量类型
 */
export type BooleanLiteral<T> = T extends boolean ? (boolean extends T ? never : T) : never

/**
 * 常量断言辅助类型
 */
export type Const<T> = T extends readonly any[]
  ? { readonly [K in keyof T]: Const<T[K]> }
  : T extends object
  ? { readonly [K in keyof T]: Const<T[K]> }
  : T

/**
 * 品牌类型（用于创建名义类型）
 */
export type Brand<T, B> = T & { readonly __brand: B }

/**
 * 创建品牌类型的辅助函数
 */
export function brand<T, B>(value: T): Brand<T, B> {
  return value as Brand<T, B>
}

/**
 * 移除品牌的辅助函数
 */
export function unbrand<T, B>(value: Brand<T, B>): T {
  return value as T
}
