import { BaseAIAdapter } from '../base/BaseAIAdapter'
import { Conversation, Message } from '@/types'
import { DeepSeekConfig } from '../configs/platformConfigs'
import { SelectorConfig } from '../base/ElementManager'

export class DeepSeekAdapter extends BaseAIAdapter {
  constructor() {
    super(DeepSeekConfig)
  }

  /**
   * 获取 DeepSeek 平台特定的选择器配置
   */
  getSelectors(): SelectorConfig {
    return {
      inputField: [
        '#chat-input',
        'textarea[placeholder*="DeepSeek"]',
        'textarea[placeholder*="发送消息"]',
        'textarea[placeholder*="输入"]',
        'textarea[placeholder*="message"]',
        'textarea[placeholder*="请输入"]'
      ],
      sendButton: [
        '.ds-button--primary',
        '[role="button"].ds-button',
        'button[aria-label*="发送"]',
        'button[aria-label*="Send"]',
        '.send-button',
        '[data-testid*="send"]'
      ],
      messageContainer: [
        '.ds-markdown',
        '._4f9bf79',
        '._9663006',
        '.message-item',
        '.chat-message',
        '.message-container',
        '.conversation'
      ],
      inputContainer: [
        '.chat-input-container',
        '.input-wrapper',
        '.editor-container',
        '.input-area'
      ]
    }
  }





  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = this.findElements(this.config.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []

      messageElements.forEach((element, index) => {
        // DeepSeek可能使用不同的类名或属性来区分用户和助手消息
        const isUser = element.classList.contains('user') ||
                      element.getAttribute('data-role') === 'user' ||
                      element.querySelector('.user-message') !== null

        const contentElement = element.querySelector('.message-content') ||
                              element.querySelector('.content') ||
                              element

        if (contentElement) {
          const content = this.cleanContent(contentElement.textContent || '')
          if (content) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      // 使用配置的标题选择器
      const titleElement = this.findElement(this.config.selectors.conversationTitle || ['.chat-title'])
      const title = titleElement.element?.textContent || `DeepSeek对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `deepseek-${Date.now()}`,
        platform: 'deepseek',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('【EchoSync】Extract DeepSeek conversation error:', error)
      return null
    }
  }

  /**
   * 提取最新的AI回答
   */
  protected async extractLatestAnswer(): Promise<string | null> {
    try {
      // 查找最新的AI回答内容（ds-markdown类）
      const answerElements = this.findElements([
        '.ds-markdown',
        '.message-content',
        ...(this.config.selectors.assistantMessage || [])
      ])

      if (answerElements.length === 0) return null

      // 获取最后一个答案
      const latestAnswer = answerElements[answerElements.length - 1]
      const content = this.cleanContent(latestAnswer.textContent || '')

      if (content.length > 0) {
        console.log('【EchoSync】DeepSeek extracted answer:', content.substring(0, 100) + '...')
        return content
      }

      return null
    } catch (error) {
      console.error('【EchoSync】Extract DeepSeek latest answer error:', error)
      return null
    }
  }
}
