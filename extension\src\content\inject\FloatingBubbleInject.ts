/**
 * 浮动气泡注入器
 * 处理浮动气泡的业务逻辑、事件监听和交互
 */

import { BaseInject } from './BaseInject'
import { FloatingBubble } from '../components/FloatingBubble'
import { 
  IEventBus, 
  InputEvent, 
  PageEvent, 
  UIEvent,
  BusinessEvent 
} from '../types/events'
import { IAIAdapter } from '../types/adapters'
import { 
  FloatingBubbleInjectConfig,
  IDragHandler,
  DragHandlerConfig 
} from '../types/components'

/**
 * 拖拽处理器实现
 */
class DragHandler implements IDragHandler {
  private element: HTMLElement
  private isEnabled: boolean = true
  private isDragging: boolean = false
  private dragStartTime: number = 0
  private dragStartPos: { x: number; y: number } = { x: 0, y: 0 }
  private elementStartPos: { x: number; y: number } = { x: 0, y: 0 }
  private longPressTimer: number | null = null
  private dragThreshold: number = 5
  private longPressDelay: number = 200

  private onDragStart?: (pos: { x: number; y: number }) => void
  private onDragMove?: (pos: { x: number; y: number }) => void
  private onDragEnd?: (pos: { x: number; y: number }) => void

  constructor(element: HTMLElement, config: DragHandlerConfig = {}) {
    this.element = element
    this.dragThreshold = config.threshold || 5
    this.setupEventListeners()
  }

  enable(): void {
    this.isEnabled = true
  }

  disable(): void {
    this.isEnabled = false
    this.stopDrag()
  }

  isEnabled(): boolean {
    return this.isEnabled
  }

  isDragging(): boolean {
    return this.isDragging
  }

  setBoundary(boundary: 'viewport' | 'parent' | HTMLElement): void {
    // 实现边界设置逻辑
  }

  destroy(): void {
    this.disable()
    this.clearLongPressTimer()
  }

  setCallbacks(callbacks: {
    onDragStart?: (pos: { x: number; y: number }) => void
    onDragMove?: (pos: { x: number; y: number }) => void
    onDragEnd?: (pos: { x: number; y: number }) => void
  }): void {
    this.onDragStart = callbacks.onDragStart
    this.onDragMove = callbacks.onDragMove
    this.onDragEnd = callbacks.onDragEnd
  }

  private setupEventListeners(): void {
    this.element.addEventListener('mousedown', this.handleMouseDown.bind(this))
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false })
  }

  private handleMouseDown(e: MouseEvent): void {
    if (!this.isEnabled) return
    
    this.startDrag(e.clientX, e.clientY)
    
    document.addEventListener('mousemove', this.handleMouseMove.bind(this))
    document.addEventListener('mouseup', this.handleMouseUp.bind(this))
  }

  private handleTouchStart(e: TouchEvent): void {
    if (!this.isEnabled) return
    
    const touch = e.touches[0]
    this.startDrag(touch.clientX, touch.clientY)
    
    document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false })
    document.addEventListener('touchend', this.handleTouchEnd.bind(this))
  }

  private startDrag(clientX: number, clientY: number): void {
    this.dragStartTime = Date.now()
    this.dragStartPos = { x: clientX, y: clientY }
    
    const rect = this.element.getBoundingClientRect()
    this.elementStartPos = { x: rect.left, y: rect.top }
    
    // 设置长按定时器
    this.longPressTimer = window.setTimeout(() => {
      this.element.style.cursor = 'grabbing'
    }, this.longPressDelay)
  }

  private handleMouseMove(e: MouseEvent): void {
    this.handleDragMove(e.clientX, e.clientY)
  }

  private handleTouchMove(e: TouchEvent): void {
    e.preventDefault()
    const touch = e.touches[0]
    this.handleDragMove(touch.clientX, touch.clientY)
  }

  private handleDragMove(clientX: number, clientY: number): void {
    const deltaX = clientX - this.dragStartPos.x
    const deltaY = clientY - this.dragStartPos.y
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    
    if (!this.isDragging && distance > this.dragThreshold) {
      this.isDragging = true
      this.clearLongPressTimer()
      this.onDragStart?.(this.elementStartPos)
    }
    
    if (this.isDragging) {
      const newPos = {
        x: this.elementStartPos.x + deltaX,
        y: this.elementStartPos.y + deltaY
      }
      this.onDragMove?.(newPos)
    }
  }

  private handleMouseUp(): void {
    this.endDrag()
    document.removeEventListener('mousemove', this.handleMouseMove.bind(this))
    document.removeEventListener('mouseup', this.handleMouseUp.bind(this))
  }

  private handleTouchEnd(): void {
    this.endDrag()
    document.removeEventListener('touchmove', this.handleTouchMove.bind(this))
    document.removeEventListener('touchend', this.handleTouchEnd.bind(this))
  }

  private endDrag(): void {
    this.clearLongPressTimer()
    
    if (this.isDragging) {
      const rect = this.element.getBoundingClientRect()
      this.onDragEnd?.({ x: rect.left, y: rect.top })
    }
    
    this.isDragging = false
    this.element.style.cursor = 'grab'
  }

  private stopDrag(): void {
    if (this.isDragging) {
      this.endDrag()
    }
  }

  private clearLongPressTimer(): void {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer)
      this.longPressTimer = null
    }
  }
}

/**
 * 浮动气泡注入器实现
 */
export class FloatingBubbleInject extends BaseInject {
  private component: FloatingBubble | null = null
  private dragHandler: DragHandler | null = null
  private hoverTimer: number | null = null
  private config: FloatingBubbleInjectConfig

  constructor(
    adapter: IAIAdapter,
    eventBus: IEventBus,
    config: FloatingBubbleInjectConfig = {}
  ) {
    super(adapter, eventBus, {
      name: 'FloatingBubbleInject',
      ...config
    })
    
    this.config = config
  }

  /**
   * 执行注入
   */
  async inject(): Promise<void> {
    try {
      // 创建UI组件
      this.component = new FloatingBubble(this.config.componentConfig)
      
      // 显示组件
      this.component.show()
      
      // 设置拖拽功能
      this.setupDragHandler()
      
      // 设置事件监听
      this.setupEventListeners()
      
      // 初始定位到输入框附近
      await this.positionNearInput()
      
      this.log('info', 'FloatingBubble injected successfully')
      
    } catch (error) {
      this.log('error', 'Failed to inject FloatingBubble:', error)
      throw error
    }
  }

  /**
   * 设置拖拽处理器
   */
  private setupDragHandler(): void {
    if (!this.component?.element) return

    this.dragHandler = new DragHandler(this.component.element, {
      threshold: 5
    })

    // 设置拖拽回调
    this.dragHandler.setCallbacks({
      onDragStart: (pos) => {
        this.eventBus.publish(UIEvent.BUBBLE_MOVE, {
          from: this.component!.getPosition(),
          to: pos
        })
      },
      onDragMove: (pos) => {
        this.component?.moveTo(pos.x, pos.y)
      },
      onDragEnd: (pos) => {
        this.component?.snapToBoundary()
        this.eventBus.publish(UIEvent.BUBBLE_MOVE, {
          from: pos,
          to: this.component!.getPosition()
        })
      }
    })
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.component?.element) return

    const element = this.component.element

    // 鼠标悬停事件
    this.addDOMEventListener(element, 'mouseenter', () => {
      this.component?.setHovered(true)
      
      // 延迟显示历史气泡
      this.hoverTimer = this.setTimeout(() => {
        this.eventBus.publish(UIEvent.BUBBLE_SHOW, {
          position: this.component!.getPosition()
        })
      }, 500)
    })

    this.addDOMEventListener(element, 'mouseleave', () => {
      this.component?.setHovered(false)
      
      // 清除悬停定时器
      if (this.hoverTimer) {
        clearTimeout(this.hoverTimer)
        this.hoverTimer = null
      }
      
      // 延迟隐藏历史气泡
      this.setTimeout(() => {
        this.eventBus.publish(UIEvent.BUBBLE_HIDE, {
          reason: 'mouse-leave'
        })
      }, 300)
    })

    // 点击事件
    this.addDOMEventListener(element, 'click', (e) => {
      // 检查是否是拖拽后的点击
      if (this.dragHandler?.isDragging()) {
        e.preventDefault()
        return
      }

      this.eventBus.publish(UIEvent.HISTORY_CLICK, {
        timestamp: Date.now()
      })
    })

    // 右键菜单事件
    this.addDOMEventListener(element, 'contextmenu', (e) => {
      e.preventDefault()
      this.eventBus.publish(UIEvent.MODAL_OPEN, {
        type: 'debug-menu',
        data: { position: this.component!.getPosition() }
      })
    })

    // 键盘事件
    this.addDOMEventListener(element, 'keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        this.eventBus.publish(UIEvent.HISTORY_CLICK, {
          timestamp: Date.now()
        })
      }
    })

    // 监听输入框变化事件
    this.addEventListner(InputEvent.FOCUSED, (event) => {
      if (event.data.element) {
        this.component?.moveToInputField(event.data.element)
      }
    })

    // 监听页面变化事件
    this.addEventListner(PageEvent.CONTENT_CHANGED, () => {
      // 延迟重新定位，确保DOM变化完成
      this.setTimeout(() => {
        this.positionNearInput()
      }, 200)
    })

    // 监听窗口大小变化
    this.addGlobalEventListener('resize', () => {
      this.component?.snapToBoundary()
    })
  }

  /**
   * 定位到输入框附近
   */
  private async positionNearInput(): Promise<void> {
    try {
      const inputElement = this.adapter.getElement('inputField' as any)
      if (inputElement && this.component) {
        this.component.moveToInputField(inputElement)
        this.log('debug', 'Positioned near input field')
      }
    } catch (error) {
      this.log('warn', 'Failed to position near input:', error)
    }
  }

  /**
   * 取消注入
   */
  protected async onUninject(): Promise<void> {
    if (this.dragHandler) {
      this.dragHandler.destroy()
      this.dragHandler = null
    }

    if (this.component) {
      this.component.destroy()
      this.component = null
    }

    this.log('info', 'FloatingBubble uninject completed')
  }

  /**
   * 销毁时清理
   */
  protected onDestroy(): void {
    if (this.hoverTimer) {
      clearTimeout(this.hoverTimer)
      this.hoverTimer = null
    }
  }
}
