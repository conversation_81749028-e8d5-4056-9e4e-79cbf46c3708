/**
 * 存档按钮UI组件
 * 专注于UI渲染和样式，不包含业务逻辑
 */

import { BaseComponent } from './BaseComponent'
import { 
  IArchiveButton, 
  ArchiveButtonConfig 
} from '../types/components'

/**
 * 按钮状态枚举
 */
export enum ArchiveButtonState {
  HIDDEN = 'hidden',
  ENABLED = 'enabled',
  DISABLED = 'disabled',
  LOADING = 'loading',
  ARCHIVED = 'archived'
}

/**
 * 存档按钮UI组件实现
 */
export class ArchiveButton extends BaseComponent implements IArchiveButton {
  private buttonState: ArchiveButtonState = ArchiveButtonState.HIDDEN
  private position: { x: number; y: number } = { x: 0, y: 0 }
  private text: string = '📁'
  private icon: string = '📁'
  private animationFrame: number | null = null
  private glowAnimation: Animation | null = null

  constructor(config: ArchiveButtonConfig = {}) {
    super({
      name: 'ArchiveButton',
      ...config
    })

    // 设置配置
    this.text = config.text || '📁'
    this.icon = config.icon || '📁'
  }

  /**
   * 渲染组件
   */
  render(): HTMLElement {
    const button = this.createElement('div', {
      'class': 'echosync-archive-button',
      'data-component': 'archive-button',
      'role': 'button',
      'tabindex': '0',
      'aria-label': '存档当前提示词',
      'title': '存档当前提示词'
    })

    // 设置基础样式
    this.applyStyles(button)

    // 设置内容
    this.updateContent(button)

    // 应用自定义样式和属性
    this.applyCustomStyles(button)
    this.applyCustomAttributes(button)

    // 初始状态为隐藏
    this.updateVisualState(button)

    return button
  }

  /**
   * 设置按钮状态
   */
  setState(state: 'enabled' | 'disabled' | 'loading'): void {
    let newState: ArchiveButtonState

    switch (state) {
      case 'enabled':
        newState = ArchiveButtonState.ENABLED
        break
      case 'disabled':
        newState = ArchiveButtonState.DISABLED
        break
      case 'loading':
        newState = ArchiveButtonState.LOADING
        break
      default:
        return
    }

    if (this.buttonState === newState) return

    this.buttonState = newState
    
    if (this._element) {
      this.updateVisualState(this._element)
    }

    this.log('debug', `Button state changed to: ${state}`)
  }

  /**
   * 设置按钮文本
   */
  setText(text: string): void {
    this.text = text
    
    if (this._element) {
      this.updateContent(this._element)
    }
  }

  /**
   * 设置按钮图标
   */
  setIcon(icon: string): void {
    this.icon = icon
    
    if (this._element) {
      this.updateContent(this._element)
    }
  }

  /**
   * 获取按钮状态
   */
  getButtonState(): 'enabled' | 'disabled' | 'loading' {
    switch (this.buttonState) {
      case ArchiveButtonState.ENABLED:
        return 'enabled'
      case ArchiveButtonState.DISABLED:
        return 'disabled'
      case ArchiveButtonState.LOADING:
        return 'loading'
      default:
        return 'disabled'
    }
  }

  /**
   * 显示按钮
   */
  showButton(): void {
    if (this.buttonState === ArchiveButtonState.HIDDEN) {
      this.setState('enabled')
    }
  }

  /**
   * 隐藏按钮
   */
  hideButton(): void {
    this.buttonState = ArchiveButtonState.HIDDEN
    
    if (this._element) {
      this.updateVisualState(this._element)
    }
  }

  /**
   * 显示已存档状态
   */
  showArchivedState(): void {
    this.buttonState = ArchiveButtonState.ARCHIVED
    
    if (this._element) {
      this.updateVisualState(this._element)
    }
  }

  /**
   * 移动到指定位置
   */
  moveTo(x: number, y: number): void {
    this.position = { x, y }
    
    if (this._element) {
      this.updatePosition(this._element)
    }
  }

  /**
   * 相对于输入容器定位
   */
  positionRelativeToInput(inputContainer: HTMLElement): void {
    if (!inputContainer) return

    const rect = inputContainer.getBoundingClientRect()
    const buttonSize = 25
    const margin = 8

    // 定位在输入框右侧，底部对齐
    let x = rect.right + margin
    let y = rect.bottom - buttonSize

    // 边界检查
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight

    x = Math.max(margin, Math.min(x, windowWidth - buttonSize - margin))
    y = Math.max(margin, Math.min(y, windowHeight - buttonSize - margin))

    this.moveTo(x, y)
  }

  /**
   * 播放存档动画
   */
  playArchiveAnimation(): void {
    if (!this._element) return

    // 创建飞行动画元素
    const flyingIcon = this._element.cloneNode(true) as HTMLElement
    flyingIcon.style.cssText = `
      position: fixed;
      z-index: 10002;
      pointer-events: none;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    `

    const buttonRect = this._element.getBoundingClientRect()
    flyingIcon.style.left = `${buttonRect.left}px`
    flyingIcon.style.top = `${buttonRect.top}px`

    document.body.appendChild(flyingIcon)

    // 动画到右上角
    setTimeout(() => {
      flyingIcon.style.left = `${window.innerWidth - 100}px`
      flyingIcon.style.top = '20px'
      flyingIcon.style.transform = 'scale(0.3)'
      flyingIcon.style.opacity = '0'
    }, 50)

    // 清理动画元素
    setTimeout(() => {
      if (document.body.contains(flyingIcon)) {
        document.body.removeChild(flyingIcon)
      }
    }, 650)
  }

  /**
   * 应用基础样式
   */
  private applyStyles(element: HTMLElement): void {
    element.style.cssText = `
      position: fixed;
      width: 25px;
      height: 25px;
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10001;
      box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: white;
      font-size: 12px;
      opacity: 0;
      transform: scale(0.8);
      pointer-events: none;
      user-select: none;
      border: none;
      outline: none;
    `
  }

  /**
   * 更新内容
   */
  private updateContent(element: HTMLElement): void {
    element.innerHTML = this.icon || this.text
  }

  /**
   * 更新位置
   */
  private updatePosition(element: HTMLElement): void {
    element.style.left = `${this.position.x}px`
    element.style.top = `${this.position.y}px`
  }

  /**
   * 更新视觉状态
   */
  private updateVisualState(element: HTMLElement): void {
    // 停止之前的动画
    this.stopGlowAnimation()

    switch (this.buttonState) {
      case ArchiveButtonState.HIDDEN:
        element.style.opacity = '0'
        element.style.transform = 'scale(0.8)'
        element.style.pointerEvents = 'none'
        element.style.background = 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
        element.innerHTML = this.icon
        break

      case ArchiveButtonState.ENABLED:
        element.style.opacity = '1'
        element.style.transform = 'scale(1)'
        element.style.pointerEvents = 'auto'
        element.style.background = 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
        element.innerHTML = this.icon
        this.startGlowAnimation(element)
        break

      case ArchiveButtonState.DISABLED:
        element.style.opacity = '0.5'
        element.style.transform = 'scale(1)'
        element.style.pointerEvents = 'none'
        element.style.background = 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'
        element.innerHTML = this.icon
        break

      case ArchiveButtonState.LOADING:
        element.style.opacity = '1'
        element.style.transform = 'scale(1)'
        element.style.pointerEvents = 'none'
        element.style.background = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
        element.innerHTML = '⏳'
        break

      case ArchiveButtonState.ARCHIVED:
        element.style.opacity = '1'
        element.style.transform = 'scale(1)'
        element.style.pointerEvents = 'none'
        element.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
        element.innerHTML = '✓'
        break
    }
  }

  /**
   * 开始发光动画
   */
  private startGlowAnimation(element: HTMLElement): void {
    // 确保样式存在
    this.ensureGlowStyles()

    // 使用Web Animations API
    this.glowAnimation = element.animate([
      {
        boxShadow: '0 4px 25px rgba(139, 92, 246, 0.4)',
        transform: 'scale(1)'
      },
      {
        boxShadow: '0 4px 35px rgba(139, 92, 246, 0.8), 0 0 30px rgba(139, 92, 246, 0.6)',
        transform: 'scale(1.05)'
      },
      {
        boxShadow: '0 4px 25px rgba(139, 92, 246, 0.4)',
        transform: 'scale(1)'
      }
    ], {
      duration: 2000,
      iterations: Infinity,
      easing: 'ease-in-out'
    })
  }

  /**
   * 停止发光动画
   */
  private stopGlowAnimation(): void {
    if (this.glowAnimation) {
      this.glowAnimation.cancel()
      this.glowAnimation = null
    }
  }

  /**
   * 确保发光样式存在
   */
  private ensureGlowStyles(): void {
    if (!document.getElementById('echosync-glow-styles')) {
      const style = document.createElement('style')
      style.id = 'echosync-glow-styles'
      style.textContent = `
        @keyframes echosync-glow {
          0%, 100% {
            box-shadow: 0 4px 25px rgba(139, 92, 246, 0.4);
            transform: scale(1);
          }
          50% {
            box-shadow: 0 4px 35px rgba(139, 92, 246, 0.8), 0 0 30px rgba(139, 92, 246, 0.6);
            transform: scale(1.05);
          }
        }
      `
      document.head.appendChild(style)
    }
  }

  /**
   * 组件销毁时的清理
   */
  protected onDestroy(): void {
    this.stopGlowAnimation()
    
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
      this.animationFrame = null
    }
  }

  /**
   * 组件更新
   */
  protected onUpdate(data?: any): void {
    if (data?.state) {
      this.setState(data.state)
    }
    
    if (data?.position) {
      this.moveTo(data.position.x, data.position.y)
    }
    
    if (data?.text) {
      this.setText(data.text)
    }
    
    if (data?.icon) {
      this.setIcon(data.icon)
    }
  }
}
