/**
 * 注入器统一导出
 * 提供所有注入器的统一入口
 */

// 基础注入器
export { BaseInject } from './BaseInject'

// 注入器实现
export { FloatingBubbleInject } from './FloatingBubbleInject'
export { ArchiveButtonInject } from './ArchiveButtonInject'
export { HistoryBubbleInject } from './HistoryBubbleInject'

// 注入器接口重新导出
export type {
  IInject,
  InjectConfig,
  FloatingBubbleInjectConfig,
  ArchiveButtonInjectConfig,
  HistoryBubbleInjectConfig,
  IDragHandler,
  DragHandlerConfig
} from '../types/components'
