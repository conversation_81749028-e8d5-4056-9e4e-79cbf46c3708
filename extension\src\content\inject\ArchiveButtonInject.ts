/**
 * 存档按钮注入器
 * 处理存档按钮的业务逻辑、事件监听和交互
 */

import { BaseInject } from './BaseInject'
import { ArchiveButton, ArchiveButtonState } from '../components/ArchiveButton'
import { 
  IEventBus, 
  InputEvent, 
  PageEvent, 
  UIEvent,
  BusinessEvent 
} from '../types/events'
import { IAIAdapter, ElementType } from '../types/adapters'
import { ArchiveButtonInjectConfig } from '../types/components'
import { MessagingService } from '@/lib/service/messagingService'
import { MessageType } from '@/types'

/**
 * 存档按钮注入器实现
 */
export class ArchiveButtonInject extends BaseInject {
  private component: ArchiveButton | null = null
  private currentPromptId: string = ''
  private archivedPromptIds: Set<string> = new Set()
  private repositionObserver: ResizeObserver | null = null
  private config: ArchiveButtonInjectConfig

  constructor(
    adapter: IAIAdapter,
    eventBus: IEventBus,
    config: ArchiveButtonInjectConfig = {}
  ) {
    super(adapter, eventBus, {
      name: 'ArchiveButtonInject',
      ...config
    })
    
    this.config = config
    this.generateNewPromptId()
  }

  /**
   * 执行注入
   */
  async inject(): Promise<void> {
    try {
      // 创建UI组件
      this.component = new ArchiveButton(this.config.componentConfig)
      
      // 显示组件
      this.component.show()
      
      // 初始定位
      await this.positionButton()
      
      // 设置事件监听
      this.setupEventListeners()
      
      // 设置位置观察器
      this.setupPositionObserver()
      
      // 初始状态更新
      this.updateButtonState()
      
      this.log('info', 'ArchiveButton injected successfully')
      
    } catch (error) {
      this.log('error', 'Failed to inject ArchiveButton:', error)
      throw error
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.component?.element) return

    const element = this.component.element

    // 点击事件
    this.addDOMEventListener(element, 'click', async (e) => {
      e.preventDefault()
      e.stopPropagation()
      
      await this.handleArchiveClick()
    })

    // 键盘事件
    this.addDOMEventListener(element, 'keydown', async (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        await this.handleArchiveClick()
      }
    })

    // 悬停事件
    this.addDOMEventListener(element, 'mouseenter', () => {
      element.style.transform = 'scale(1.1)'
    })

    this.addDOMEventListener(element, 'mouseleave', () => {
      element.style.transform = 'scale(1)'
    })

    // 监听输入变化事件
    this.addEventListner(InputEvent.CHANGED, (event) => {
      this.updateButtonState(event.data.value)
    })

    // 监听输入聚焦事件
    this.addEventListner(InputEvent.FOCUSED, () => {
      this.positionButton()
      this.updateButtonState()
    })

    // 监听页面变化事件
    this.addEventListner(PageEvent.CONTENT_CHANGED, () => {
      // 延迟重新定位，确保DOM变化完成
      this.setTimeout(() => {
        this.positionButton()
      }, 200)
    })

    // 监听窗口大小变化
    this.addGlobalEventListener('resize', () => {
      this.positionButton()
    })

    // 监听滚动事件
    this.addGlobalEventListener('scroll', () => {
      this.positionButton()
    })

    // 监听存档请求事件（来自其他组件）
    this.addEventListner(BusinessEvent.ARCHIVE_REQUESTED, async (event) => {
      if (event.data.promptId === this.currentPromptId) {
        await this.performArchive(event.data.content)
      }
    })
  }

  /**
   * 设置位置观察器
   */
  private setupPositionObserver(): void {
    const inputElement = this.adapter.getElement(ElementType.INPUT_FIELD)
    if (!inputElement) return

    // 观察输入框及其容器的大小变化
    this.repositionObserver = new ResizeObserver(() => {
      this.positionButton()
    })

    this.repositionObserver.observe(inputElement)
    
    // 也观察输入容器
    const inputContainer = this.adapter.getElement(ElementType.INPUT_CONTAINER)
    if (inputContainer && inputContainer !== inputElement) {
      this.repositionObserver.observe(inputContainer)
    }
  }

  /**
   * 定位按钮
   */
  private async positionButton(): Promise<void> {
    if (!this.component) return

    try {
      // 优先使用输入容器
      let targetElement = this.adapter.getElement(ElementType.INPUT_CONTAINER)
      
      // 如果没有输入容器，使用输入框
      if (!targetElement) {
        targetElement = this.adapter.getElement(ElementType.INPUT_FIELD)
      }

      if (targetElement) {
        this.component.positionRelativeToInput(targetElement)
        this.log('debug', 'Button positioned relative to input')
      }
    } catch (error) {
      this.log('warn', 'Failed to position button:', error)
    }
  }

  /**
   * 更新按钮状态
   */
  private updateButtonState(inputValue?: string): void {
    if (!this.component) return

    // 获取当前输入内容
    let content = inputValue
    if (!content) {
      content = this.adapter.getCurrentInput()
    }

    const hasContent = (content || '').trim().length > 0
    const isArchived = this.archivedPromptIds.has(this.currentPromptId)

    if (hasContent && !isArchived) {
      // 显示按钮并启用
      this.component.setState('enabled')
      this.component.showButton()
    } else if (isArchived) {
      // 显示已存档状态
      this.component.showArchivedState()
    } else {
      // 隐藏按钮
      this.component.hideButton()
    }
  }

  /**
   * 处理存档点击
   */
  private async handleArchiveClick(): Promise<void> {
    if (!this.component) return

    try {
      // 获取当前输入内容
      const currentPrompt = this.adapter.getCurrentInput()
      if (!currentPrompt || currentPrompt.trim().length === 0) {
        this.log('warn', 'Input is empty, cannot archive')
        return
      }

      // 检查是否已存档
      if (this.archivedPromptIds.has(this.currentPromptId)) {
        this.log('warn', 'Prompt already archived')
        return
      }

      // 设置加载状态
      this.component.setState('loading')

      // 执行存档
      await this.performArchive(currentPrompt)

    } catch (error) {
      this.log('error', 'Archive click handling failed:', error)
      
      // 恢复按钮状态
      this.component?.setState('enabled')
    }
  }

  /**
   * 执行存档操作
   */
  private async performArchive(content: string): Promise<void> {
    try {
      // 获取平台信息
      const platformName = this.adapter.config.name
      if (!platformName) {
        throw new Error('Cannot identify current platform')
      }

      // 发布存档请求事件
      this.eventBus.publish(BusinessEvent.ARCHIVE_REQUESTED, {
        content,
        promptId: this.currentPromptId,
        metadata: {
          platform: platformName,
          timestamp: Date.now()
        }
      })

      // 准备存档数据
      const archiveData = {
        chat_prompt: content,
        chat_uid: this.currentPromptId,
        platform_name: platformName,
        create_time: Date.now()
      }

      // 发送到后台
      const result = await MessagingService.sendToBackground(
        MessageType.DB_CHAT_HISTORY_CREATE, 
        archiveData
      )

      if (result.success) {
        // 标记为已存档
        this.archivedPromptIds.add(this.currentPromptId)
        
        // 更新UI状态
        this.component?.showArchivedState()
        
        // 播放存档动画
        this.component?.playArchiveAnimation()
        
        // 发布存档完成事件
        this.eventBus.publish(BusinessEvent.ARCHIVE_COMPLETED, {
          promptId: this.currentPromptId,
          success: true,
          data: result.data
        })

        this.log('info', 'Prompt archived successfully:', result.data)
        
      } else {
        throw new Error(result.error || 'Archive failed')
      }

    } catch (error) {
      this.log('error', 'Archive operation failed:', error)
      
      // 发布存档失败事件
      this.eventBus.publish(BusinessEvent.ARCHIVE_FAILED, {
        promptId: this.currentPromptId,
        error: error as Error
      })
      
      // 恢复按钮状态
      this.component?.setState('enabled')
      
      throw error
    }
  }

  /**
   * 自动存档（发送时调用）
   */
  async autoArchivePrompt(promptContent: string): Promise<void> {
    try {
      // 生成新的提示词ID
      this.generateNewPromptId()
      
      // 执行存档
      await this.performArchive(promptContent)
      
      this.log('info', 'Auto archive completed for prompt:', this.currentPromptId)
      
    } catch (error) {
      this.log('error', 'Auto archive failed:', error)
    }
  }

  /**
   * 生成新的提示词ID
   */
  generateNewPromptId(): void {
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)
    this.currentPromptId = `prompt-${timestamp}-${randomStr}`
    
    this.log('debug', 'Generated new prompt ID:', this.currentPromptId)
  }

  /**
   * 获取当前提示词ID
   */
  getCurrentPromptId(): string {
    return this.currentPromptId
  }

  /**
   * 设置当前提示词ID
   */
  setCurrentPromptId(promptId: string): void {
    this.currentPromptId = promptId
    this.updateButtonState()
  }

  /**
   * 标记为已存档
   */
  markAsArchived(): void {
    this.archivedPromptIds.add(this.currentPromptId)
    this.component?.showArchivedState()
  }

  /**
   * 获取已存档的提示词ID集合
   */
  getArchivedPromptIds(): Set<string> {
    return new Set(this.archivedPromptIds)
  }

  /**
   * 取消注入
   */
  protected async onUninject(): Promise<void> {
    // 清理位置观察器
    if (this.repositionObserver) {
      this.repositionObserver.disconnect()
      this.repositionObserver = null
    }

    // 销毁UI组件
    if (this.component) {
      this.component.destroy()
      this.component = null
    }

    this.log('info', 'ArchiveButton uninject completed')
  }

  /**
   * 销毁时清理
   */
  protected onDestroy(): void {
    // 清理已存档ID集合
    this.archivedPromptIds.clear()
  }
}
