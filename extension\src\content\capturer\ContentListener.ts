/**
 * 内容监听器
 * 统一管理页面事件监听，监听输入框变化、页面变化等事件
 */

import { 
  IContentListener, 
  SelectorConfig 
} from '../types/adapters'
import { 
  IEventBus, 
  InputEvent, 
  PageEvent, 
  ElementEvent 
} from '../types/events'
import { ILogger } from '../types/utils'

/**
 * 内容监听器实现
 */
export class ContentListener implements IContentListener {
  private eventBus: IEventBus
  private logger?: ILogger
  private isListening: boolean = false
  private selectors: SelectorConfig | null = null
  
  // 监听器清理函数
  private cleanupFunctions: (() => void)[] = []
  
  // 观察器实例
  private mutationObserver: MutationObserver | null = null
  private intersectionObserver: IntersectionObserver | null = null
  
  // 缓存的元素引用
  private cachedElements: Map<string, HTMLElement> = new Map()
  
  // 防抖定时器
  private debounceTimers: Map<string, number> = new Map()
  
  // 监听配置
  private config = {
    debounceDelay: 300,
    mutationThrottle: 100,
    enableIntersectionObserver: true,
    enableResizeObserver: true
  }

  constructor(eventBus: IEventBus, logger?: ILogger) {
    this.eventBus = eventBus
    this.logger = logger
  }

  /**
   * 开始监听
   */
  startListening(selectors: SelectorConfig): void {
    if (this.isListening) {
      this.log('warn', 'Already listening, stopping previous listeners first')
      this.stopListening()
    }

    this.selectors = selectors
    this.isListening = true

    try {
      // 初始化各种监听器
      this.initializeInputListeners()
      this.initializeMutationObserver()
      this.initializePageListeners()
      this.initializeElementObservers()

      this.log('info', 'Content listener started successfully')
    } catch (error) {
      this.log('error', 'Failed to start content listener:', error)
      this.stopListening()
    }
  }

  /**
   * 停止监听
   */
  stopListening(): void {
    if (!this.isListening) {
      return
    }

    try {
      // 清理所有监听器
      this.cleanupFunctions.forEach(cleanup => {
        try {
          cleanup()
        } catch (error) {
          this.log('warn', 'Error during cleanup:', error)
        }
      })
      this.cleanupFunctions = []

      // 清理观察器
      this.cleanupObservers()

      // 清理定时器
      this.clearAllTimers()

      // 清理缓存
      this.cachedElements.clear()

      this.isListening = false
      this.selectors = null

      this.log('info', 'Content listener stopped')
    } catch (error) {
      this.log('error', 'Error during stop listening:', error)
    }
  }

  /**
   * 是否正在监听
   */
  isListening(): boolean {
    return this.isListening
  }

  /**
   * 销毁监听器
   */
  destroy(): void {
    this.stopListening()
    this.log('info', 'Content listener destroyed')
  }

  /**
   * 初始化输入框监听器
   */
  private initializeInputListeners(): void {
    if (!this.selectors) return

    const inputSelectors = this.selectors.inputField
    
    inputSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector) as NodeListOf<HTMLElement>
        
        elements.forEach((element, index) => {
          this.setupInputElementListeners(element, `${selector}_${index}`)
        })
      } catch (error) {
        this.log('warn', `Invalid input selector: ${selector}`, error)
      }
    })
  }

  /**
   * 设置单个输入元素的监听器
   */
  private setupInputElementListeners(element: HTMLElement, key: string): void {
    // 缓存元素
    this.cachedElements.set(key, element)

    // 焦点事件
    const focusHandler = (event: FocusEvent) => {
      this.debounce(`focus_${key}`, () => {
        this.eventBus.publish(InputEvent.FOCUSED, {
          element: element,
          value: this.getElementValue(element)
        })
      })
    }

    // 失焦事件
    const blurHandler = (event: FocusEvent) => {
      this.debounce(`blur_${key}`, () => {
        this.eventBus.publish(InputEvent.BLURRED, {
          element: element,
          value: this.getElementValue(element)
        })
      })
    }

    // 输入变化事件
    const inputHandler = (event: Event) => {
      this.debounce(`input_${key}`, () => {
        this.eventBus.publish(InputEvent.CHANGED, {
          element: element,
          value: this.getElementValue(element)
        })
      })
    }

    // 点击事件
    const clickHandler = (event: MouseEvent) => {
      this.eventBus.publish(InputEvent.CLICKED, {
        element: element,
        position: { x: event.clientX, y: event.clientY }
      })
    }

    // 添加事件监听器
    element.addEventListener('focus', focusHandler)
    element.addEventListener('blur', blurHandler)
    element.addEventListener('input', inputHandler)
    element.addEventListener('click', clickHandler)

    // 对于contenteditable元素，还需要监听其他事件
    if (element.contentEditable === 'true') {
      const compositionHandler = () => {
        this.debounce(`composition_${key}`, () => {
          this.eventBus.publish(InputEvent.CHANGED, {
            element: element,
            value: this.getElementValue(element)
          })
        })
      }

      element.addEventListener('compositionend', compositionHandler)
      
      this.cleanupFunctions.push(() => {
        element.removeEventListener('compositionend', compositionHandler)
      })
    }

    // 添加清理函数
    this.cleanupFunctions.push(() => {
      element.removeEventListener('focus', focusHandler)
      element.removeEventListener('blur', blurHandler)
      element.removeEventListener('input', inputHandler)
      element.removeEventListener('click', clickHandler)
    })

    this.log('debug', `Input listeners setup for element: ${key}`)
  }

  /**
   * 初始化DOM变化观察器
   */
  private initializeMutationObserver(): void {
    this.mutationObserver = new MutationObserver((mutations) => {
      this.debounce('mutation', () => {
        this.handleMutations(mutations)
      }, this.config.mutationThrottle)
    })

    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style', 'data-*']
    })

    this.log('debug', 'Mutation observer initialized')
  }

  /**
   * 处理DOM变化
   */
  private handleMutations(mutations: MutationRecord[]): void {
    let hasRelevantChanges = false

    mutations.forEach(mutation => {
      // 检查是否影响到我们关心的元素
      if (this.isRelevantMutation(mutation)) {
        hasRelevantChanges = true
      }
    })

    if (hasRelevantChanges) {
      // 发布页面内容变化事件
      this.eventBus.publish(PageEvent.CONTENT_CHANGED, {
        url: window.location.href,
        mutations
      })

      // 重新检查输入元素
      this.recheckInputElements()
    }
  }

  /**
   * 检查变化是否相关
   */
  private isRelevantMutation(mutation: MutationRecord): boolean {
    if (!this.selectors) return false

    const target = mutation.target as HTMLElement
    
    // 检查是否影响输入框或消息容器
    const relevantSelectors = [
      ...this.selectors.inputField,
      ...this.selectors.messageContainer,
      ...this.selectors.sendButton
    ]

    return relevantSelectors.some(selector => {
      try {
        return target.matches?.(selector) || target.querySelector?.(selector)
      } catch {
        return false
      }
    })
  }

  /**
   * 重新检查输入元素
   */
  private recheckInputElements(): void {
    if (!this.selectors) return

    // 查找新的输入元素
    this.selectors.inputField.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector) as NodeListOf<HTMLElement>
        
        elements.forEach((element, index) => {
          const key = `${selector}_${index}`
          
          // 如果是新元素，设置监听器
          if (!this.cachedElements.has(key)) {
            this.setupInputElementListeners(element, key)
            
            // 发布元素发现事件
            this.eventBus.publish(ElementEvent.FOUND, {
              type: 'inputField',
              element: element,
              selector: selector
            })
          }
        })
      } catch (error) {
        this.log('warn', `Error rechecking selector: ${selector}`, error)
      }
    })
  }

  /**
   * 初始化页面级监听器
   */
  private initializePageListeners(): void {
    // URL变化监听
    let currentUrl = window.location.href
    const urlCheckInterval = setInterval(() => {
      const newUrl = window.location.href
      if (newUrl !== currentUrl) {
        this.eventBus.publish(PageEvent.URL_CHANGED, {
          oldUrl: currentUrl,
          newUrl: newUrl
        })
        currentUrl = newUrl
      }
    }, 1000)

    // 页面加载完成
    if (document.readyState === 'loading') {
      const loadHandler = () => {
        this.eventBus.publish(PageEvent.LOADED, {
          url: window.location.href,
          loadTime: performance.now()
        })
      }
      
      document.addEventListener('DOMContentLoaded', loadHandler)
      this.cleanupFunctions.push(() => {
        document.removeEventListener('DOMContentLoaded', loadHandler)
      })
    }

    // 窗口大小变化
    const resizeHandler = () => {
      this.debounce('resize', () => {
        this.recheckInputElements()
      })
    }

    window.addEventListener('resize', resizeHandler)

    // 添加清理函数
    this.cleanupFunctions.push(() => {
      clearInterval(urlCheckInterval)
      window.removeEventListener('resize', resizeHandler)
    })
  }

  /**
   * 初始化元素观察器
   */
  private initializeElementObservers(): void {
    if (!this.config.enableIntersectionObserver) return

    // 交集观察器，用于检测元素可见性变化
    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const element = entry.target as HTMLElement
        
        this.eventBus.publish(ElementEvent.STATE_CHANGED, {
          element: element,
          oldState: { visible: !entry.isIntersecting },
          newState: { visible: entry.isIntersecting }
        })
      })
    })

    // 观察缓存的元素
    this.cachedElements.forEach(element => {
      this.intersectionObserver?.observe(element)
    })
  }

  /**
   * 获取元素值
   */
  private getElementValue(element: HTMLElement): string {
    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      return (element as HTMLInputElement).value
    }
    
    if (element.contentEditable === 'true') {
      return element.textContent || ''
    }
    
    return element.textContent || ''
  }

  /**
   * 防抖处理
   */
  private debounce(key: string, func: () => void, delay: number = this.config.debounceDelay): void {
    // 清除之前的定时器
    const existingTimer = this.debounceTimers.get(key)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // 设置新的定时器
    const timerId = window.setTimeout(() => {
      func()
      this.debounceTimers.delete(key)
    }, delay)

    this.debounceTimers.set(key, timerId)
  }

  /**
   * 清理所有定时器
   */
  private clearAllTimers(): void {
    this.debounceTimers.forEach(timerId => {
      clearTimeout(timerId)
    })
    this.debounceTimers.clear()
  }

  /**
   * 清理观察器
   */
  private cleanupObservers(): void {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect()
      this.mutationObserver = null
    }

    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect()
      this.intersectionObserver = null
    }
  }

  /**
   * 记录日志
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: any): void {
    const fullMessage = `[ContentListener] ${message}`
    
    if (this.logger) {
      this.logger[level](fullMessage, context)
    } else {
      console[level](fullMessage, context)
    }
  }
}
