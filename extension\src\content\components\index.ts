/**
 * UI组件统一导出
 * 提供所有UI组件的统一入口
 */

// 基础组件
export { BaseComponent } from './BaseComponent'

// UI组件
export { FloatingBubble } from './FloatingBubble'
export { ArchiveButton, ArchiveButtonState } from './ArchiveButton'
export { HistoryBubble, type HistoryItem } from './HistoryBubble'

// 组件接口重新导出
export type {
  IComponent,
  IFloatingBubble,
  IArchiveButton,
  IHistoryBubble,
  ComponentConfig,
  FloatingBubbleConfig,
  ArchiveButtonConfig,
  HistoryBubbleConfig,
  ComponentLifecycleState
} from '../types/components'
