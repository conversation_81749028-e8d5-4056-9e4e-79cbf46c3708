/**
 * 历史气泡UI组件
 * 专注于UI渲染和样式，不包含业务逻辑
 */

import { BaseComponent } from './BaseComponent'
import { 
  IHistoryBubble, 
  HistoryBubbleConfig 
} from '../types/components'

/**
 * 历史记录项接口
 */
export interface HistoryItem {
  id: string
  chat_uid: string
  chat_prompt: string
  platform_name: string
  create_time: number
  platform_icon?: string
}

/**
 * 历史气泡UI组件实现
 */
export class HistoryBubble extends BaseComponent implements IHistoryBubble {
  private historyData: HistoryItem[] = []
  private filteredData: HistoryItem[] = []
  private searchQuery: string = ''
  private currentPage: number = 1
  private isModalOpen: boolean = false
  private modalElement: HTMLElement | null = null
  private config: HistoryBubbleConfig

  constructor(config: HistoryBubbleConfig = {}) {
    super({
      name: 'HistoryBubble',
      ...config
    })

    this.config = {
      maxItems: 10,
      searchable: true,
      paginated: false,
      itemsPerPage: 10,
      modalSize: 'medium',
      ...config
    }
  }

  /**
   * 渲染组件
   */
  render(): HTMLElement {
    const bubble = this.createElement('div', {
      'id': 'echosync-history-bubble',
      'class': 'echosync-history-bubble',
      'data-component': 'history-bubble',
      'role': 'dialog',
      'aria-label': '历史提示词'
    })

    // 设置基础样式
    this.applyStyles(bubble)

    // 渲染内容
    this.renderContent(bubble)

    // 应用自定义样式和属性
    this.applyCustomStyles(bubble)
    this.applyCustomAttributes(bubble)

    // 初始状态为隐藏
    bubble.style.display = 'none'

    return bubble
  }

  /**
   * 打开历史模态框
   */
  openModal(): void {
    if (this.isModalOpen) return

    this.createModal()
    this.isModalOpen = true
    
    this.log('debug', 'History modal opened')
  }

  /**
   * 关闭历史模态框
   */
  closeModal(): void {
    if (!this.isModalOpen || !this.modalElement) return

    // 添加关闭动画
    this.modalElement.style.opacity = '0'
    this.modalElement.style.transform = 'scale(0.95)'

    setTimeout(() => {
      if (this.modalElement && document.body.contains(this.modalElement)) {
        document.body.removeChild(this.modalElement)
      }
      this.modalElement = null
      this.isModalOpen = false
    }, 200)

    this.log('debug', 'History modal closed')
  }

  /**
   * 刷新历史数据
   */
  async refreshHistory(): Promise<void> {
    // 这个方法会被注入器调用来更新数据
    this.filterAndRenderData()
    this.log('debug', 'History data refreshed')
  }

  /**
   * 搜索历史记录
   */
  searchHistory(query: string): void {
    this.searchQuery = query.toLowerCase()
    this.currentPage = 1
    this.filterAndRenderData()
    
    this.log('debug', `Searching history with query: ${query}`)
  }

  /**
   * 获取历史记录
   */
  getHistory(): HistoryItem[] {
    return [...this.historyData]
  }

  /**
   * 更新历史数据
   */
  updateHistory(data: HistoryItem[]): void {
    this.historyData = data
    this.filterAndRenderData()
  }

  /**
   * 显示气泡
   */
  showBubble(anchorElement: HTMLElement): void {
    if (!this._element) return

    // 计算位置
    this.positionBubble(anchorElement)

    // 显示动画
    this._element.style.display = 'block'
    this._element.style.pointerEvents = 'auto'

    requestAnimationFrame(() => {
      if (this._element) {
        this._element.style.opacity = '1'
        this._element.style.transform = 'scale(1) translateY(0)'
      }
    })

    this._isVisible = true
  }

  /**
   * 隐藏气泡
   */
  hideBubble(): void {
    if (!this._element || !this._isVisible) return

    this._element.style.opacity = '0'
    this._element.style.transform = 'scale(0.9) translateY(10px)'
    this._element.style.pointerEvents = 'none'

    setTimeout(() => {
      if (this._element) {
        this._element.style.display = 'none'
      }
    }, 200)

    this._isVisible = false
  }

  /**
   * 应用基础样式
   */
  private applyStyles(element: HTMLElement): void {
    element.style.cssText = `
      position: fixed;
      z-index: 10002;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.2);
      max-width: 320px;
      max-height: 400px;
      overflow-y: auto;
      opacity: 0;
      transform: scale(0.9) translateY(10px);
      transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
      pointer-events: none;
      user-select: none;
    `

    // 添加滚动条样式
    this.addScrollbarStyles()
  }

  /**
   * 渲染内容
   */
  private renderContent(container: HTMLElement): void {
    container.innerHTML = ''

    // 添加标题
    const header = this.createElement('div', {
      'class': 'echosync-history-header'
    }, {
      padding: '12px 16px 8px',
      borderBottom: '1px solid rgba(0, 0, 0, 0.05)',
      background: 'rgba(139, 92, 246, 0.02)'
    })

    const title = this.createElement('h3', {
      'class': 'echosync-history-title'
    }, {
      fontSize: '13px',
      fontWeight: '600',
      color: '#8b5cf6',
      margin: '0'
    }, '最近提示词')

    header.appendChild(title)
    container.appendChild(header)

    // 添加搜索框（如果启用）
    if (this.config.searchable) {
      const searchContainer = this.createSearchBox()
      container.appendChild(searchContainer)
    }

    // 添加历史记录列表
    const listContainer = this.createElement('div', {
      'class': 'echosync-history-list'
    })

    this.renderHistoryList(listContainer)
    container.appendChild(listContainer)

    // 添加分页（如果启用）
    if (this.config.paginated && this.filteredData.length > this.config.itemsPerPage!) {
      const pagination = this.createPagination()
      container.appendChild(pagination)
    }
  }

  /**
   * 创建搜索框
   */
  private createSearchBox(): HTMLElement {
    const searchContainer = this.createElement('div', {
      'class': 'echosync-history-search'
    }, {
      padding: '8px 16px',
      borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
    })

    const searchInput = this.createElement('input', {
      'type': 'text',
      'placeholder': '搜索历史记录...',
      'class': 'echosync-search-input'
    }, {
      width: '100%',
      padding: '6px 12px',
      border: '1px solid rgba(0, 0, 0, 0.1)',
      borderRadius: '6px',
      fontSize: '12px',
      outline: 'none',
      background: 'rgba(255, 255, 255, 0.8)'
    }) as HTMLInputElement

    // 搜索事件会由注入器处理
    searchInput.addEventListener('input', (e) => {
      const query = (e.target as HTMLInputElement).value
      this.searchHistory(query)
    })

    searchContainer.appendChild(searchInput)
    return searchContainer
  }

  /**
   * 渲染历史记录列表
   */
  private renderHistoryList(container: HTMLElement): void {
    container.innerHTML = ''

    if (this.filteredData.length === 0) {
      const emptyDiv = this.createElement('div', {
        'class': 'echosync-history-empty'
      }, {
        padding: '20px',
        textAlign: 'center',
        color: '#666',
        fontSize: '12px'
      }, this.searchQuery ? '未找到匹配的记录' : '暂无历史提示词')

      container.appendChild(emptyDiv)
      return
    }

    // 计算分页数据
    const startIndex = this.config.paginated ? (this.currentPage - 1) * this.config.itemsPerPage! : 0
    const endIndex = this.config.paginated ? startIndex + this.config.itemsPerPage! : this.filteredData.length
    const pageData = this.filteredData.slice(startIndex, endIndex)

    // 渲染历史记录项
    pageData.forEach((item, index) => {
      const historyItem = this.createHistoryItem(item, startIndex + index)
      container.appendChild(historyItem)
    })
  }

  /**
   * 创建历史记录项
   */
  private createHistoryItem(item: HistoryItem, index: number): HTMLElement {
    const itemElement = this.createElement('div', {
      'class': 'echosync-history-item',
      'data-chat-id': item.id,
      'data-chat-uid': item.chat_uid,
      'tabindex': '0',
      'role': 'button'
    }, {
      padding: '12px 16px',
      borderBottom: '1px solid rgba(0, 0, 0, 0.05)',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      position: 'relative'
    })

    // 悬停效果
    itemElement.addEventListener('mouseenter', () => {
      itemElement.style.background = 'rgba(139, 92, 246, 0.05)'
    })

    itemElement.addEventListener('mouseleave', () => {
      itemElement.style.background = 'transparent'
    })

    // 创建内容容器
    const content = this.createElement('div', {
      'class': 'echosync-history-item-content'
    }, {
      display: 'flex',
      alignItems: 'flex-start',
      gap: '8px'
    })

    // 平台图标
    if (item.platform_icon) {
      const icon = this.createElement('div', {
        'class': 'echosync-platform-icon'
      }, {
        width: '16px',
        height: '16px',
        flexShrink: '0',
        marginTop: '2px'
      })
      icon.innerHTML = item.platform_icon
      content.appendChild(icon)
    }

    // 文本内容
    const textDiv = this.createElement('div', {
      'class': 'echosync-history-item-text'
    }, {
      flex: '1',
      minWidth: '0'
    })

    // 提示词内容
    const promptDiv = this.createElement('div', {
      'class': 'echosync-history-item-prompt'
    }, {
      fontSize: '12px',
      lineHeight: '1.4',
      color: '#333',
      marginBottom: '4px',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      display: '-webkit-box',
      WebkitLineClamp: '2',
      WebkitBoxOrient: 'vertical'
    })
    promptDiv.textContent = item.chat_prompt
    promptDiv.title = item.chat_prompt

    // 时间和平台信息
    const metaDiv = this.createElement('div', {
      'class': 'echosync-history-item-meta'
    }, {
      fontSize: '10px',
      color: '#999',
      display: 'flex',
      alignItems: 'center',
      gap: '8px'
    })

    const timeSpan = this.createElement('span', {}, {}, this.formatTime(item.create_time))
    const platformSpan = this.createElement('span', {}, {}, item.platform_name)

    metaDiv.appendChild(timeSpan)
    metaDiv.appendChild(this.createElement('span', {}, {}, '•'))
    metaDiv.appendChild(platformSpan)

    textDiv.appendChild(promptDiv)
    textDiv.appendChild(metaDiv)
    content.appendChild(textDiv)
    itemElement.appendChild(content)

    return itemElement
  }

  /**
   * 创建分页控件
   */
  private createPagination(): HTMLElement {
    const totalPages = Math.ceil(this.filteredData.length / this.config.itemsPerPage!)
    
    const pagination = this.createElement('div', {
      'class': 'echosync-history-pagination'
    }, {
      padding: '8px 16px',
      borderTop: '1px solid rgba(0, 0, 0, 0.05)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      gap: '8px'
    })

    // 上一页按钮
    const prevBtn = this.createElement('button', {
      'class': 'echosync-pagination-btn',
      'disabled': this.currentPage === 1 ? 'true' : ''
    }, {
      padding: '4px 8px',
      border: '1px solid rgba(0, 0, 0, 0.1)',
      borderRadius: '4px',
      background: 'white',
      cursor: this.currentPage === 1 ? 'not-allowed' : 'pointer',
      fontSize: '10px'
    }, '‹')

    // 页码信息
    const pageInfo = this.createElement('span', {
      'class': 'echosync-pagination-info'
    }, {
      fontSize: '10px',
      color: '#666'
    }, `${this.currentPage} / ${totalPages}`)

    // 下一页按钮
    const nextBtn = this.createElement('button', {
      'class': 'echosync-pagination-btn',
      'disabled': this.currentPage === totalPages ? 'true' : ''
    }, {
      padding: '4px 8px',
      border: '1px solid rgba(0, 0, 0, 0.1)',
      borderRadius: '4px',
      background: 'white',
      cursor: this.currentPage === totalPages ? 'not-allowed' : 'pointer',
      fontSize: '10px'
    }, '›')

    pagination.appendChild(prevBtn)
    pagination.appendChild(pageInfo)
    pagination.appendChild(nextBtn)

    return pagination
  }

  /**
   * 创建模态框
   */
  private createModal(): void {
    // 创建遮罩层
    const overlay = this.createElement('div', {
      'class': 'echosync-modal-overlay'
    }, {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      background: 'rgba(0, 0, 0, 0.5)',
      zIndex: '10003',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      opacity: '0',
      transition: 'opacity 200ms ease'
    })

    // 创建模态框内容
    const modal = this.createElement('div', {
      'class': 'echosync-history-modal'
    }, {
      background: 'white',
      borderRadius: '12px',
      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2)',
      maxWidth: this.getModalWidth(),
      maxHeight: '80vh',
      width: '90vw',
      overflow: 'hidden',
      transform: 'scale(0.95)',
      transition: 'transform 200ms ease'
    })

    // 渲染模态框内容
    this.renderModalContent(modal)

    overlay.appendChild(modal)
    document.body.appendChild(overlay)

    // 显示动画
    requestAnimationFrame(() => {
      overlay.style.opacity = '1'
      modal.style.transform = 'scale(1)'
    })

    this.modalElement = overlay

    // 点击遮罩关闭
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        this.closeModal()
      }
    })
  }

  /**
   * 渲染模态框内容
   */
  private renderModalContent(modal: HTMLElement): void {
    // 模态框标题栏
    const header = this.createElement('div', {
      'class': 'echosync-modal-header'
    }, {
      padding: '16px 20px',
      borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    })

    const title = this.createElement('h2', {}, {
      margin: '0',
      fontSize: '16px',
      fontWeight: '600',
      color: '#333'
    }, '历史提示词')

    const closeBtn = this.createElement('button', {
      'class': 'echosync-modal-close'
    }, {
      background: 'none',
      border: 'none',
      fontSize: '20px',
      cursor: 'pointer',
      padding: '4px',
      borderRadius: '4px',
      color: '#666'
    }, '×')

    closeBtn.addEventListener('click', () => this.closeModal())

    header.appendChild(title)
    header.appendChild(closeBtn)
    modal.appendChild(header)

    // 模态框内容区域
    const content = this.createElement('div', {
      'class': 'echosync-modal-content'
    }, {
      padding: '0',
      maxHeight: 'calc(80vh - 60px)',
      overflowY: 'auto'
    })

    // 在模态框中渲染历史记录
    this.renderContent(content)

    modal.appendChild(content)
  }

  /**
   * 过滤和渲染数据
   */
  private filterAndRenderData(): void {
    // 过滤数据
    this.filteredData = this.historyData.filter(item => {
      if (!this.searchQuery) return true
      return item.chat_prompt.toLowerCase().includes(this.searchQuery) ||
             item.platform_name.toLowerCase().includes(this.searchQuery)
    })

    // 重新渲染
    if (this._element) {
      this.renderContent(this._element)
    }
  }

  /**
   * 定位气泡
   */
  private positionBubble(anchorElement: HTMLElement): void {
    if (!this._element) return

    const rect = anchorElement.getBoundingClientRect()
    const bubbleHeight = Math.min(400, this.filteredData.length * 60 + 100)
    
    let top = rect.top - bubbleHeight - 10
    let left = rect.left + (rect.width / 2) - 160

    // 边界检查
    if (top < 10) {
      top = rect.bottom + 10
    }
    if (left < 10) {
      left = 10
    }
    if (left + 320 > window.innerWidth - 10) {
      left = window.innerWidth - 330
    }

    this._element.style.top = `${top}px`
    this._element.style.left = `${left}px`
  }

  /**
   * 获取模态框宽度
   */
  private getModalWidth(): string {
    switch (this.config.modalSize) {
      case 'small': return '400px'
      case 'large': return '800px'
      case 'fullscreen': return '95vw'
      default: return '600px'
    }
  }

  /**
   * 添加滚动条样式
   */
  private addScrollbarStyles(): void {
    if (!document.getElementById('echosync-scrollbar-styles')) {
      const style = document.createElement('style')
      style.id = 'echosync-scrollbar-styles'
      style.textContent = `
        .echosync-history-bubble::-webkit-scrollbar {
          width: 4px;
        }
        .echosync-history-bubble::-webkit-scrollbar-track {
          background: transparent;
        }
        .echosync-history-bubble::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 2px;
        }
        .echosync-history-bubble::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.3);
        }
      `
      document.head.appendChild(style)
    }
  }

  /**
   * 格式化时间
   */
  private formatTime(timestamp: number): string {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    
    return new Date(timestamp).toLocaleDateString()
  }

  /**
   * 组件销毁时的清理
   */
  protected onDestroy(): void {
    if (this.isModalOpen) {
      this.closeModal()
    }

    // 清理样式
    const scrollbarStyles = document.getElementById('echosync-scrollbar-styles')
    if (scrollbarStyles) {
      scrollbarStyles.remove()
    }
  }

  /**
   * 组件更新
   */
  protected onUpdate(data?: any): void {
    if (data?.history) {
      this.updateHistory(data.history)
    }
    
    if (data?.searchQuery !== undefined) {
      this.searchHistory(data.searchQuery)
    }
    
    if (data?.page) {
      this.currentPage = data.page
      this.filterAndRenderData()
    }
  }
}
