import { BaseAIAdapter } from '../base/BaseAIAdapter'
import { Conversation, Message } from '@/types'
import { ChatGPTConfig } from '../configs/platformConfigs'
import { SelectorConfig } from '../base/ElementManager'

export class ChatGPTAdapter extends BaseAIAdapter {
  constructor() {
    super(ChatGPTConfig)
  }

  /**
   * 获取 ChatGPT 平台特定的选择器配置
   */
  getSelectors(): SelectorConfig {
    return {
      inputField: [
        '#prompt-textarea',
        'textarea[placeholder*="Message"]'
      ],
      sendButton: [
        'button[data-testid="send-button"]',
        'button[aria-label="Send prompt"]'
      ],
      messageContainer: [
        '[data-message-author-role]',
        '.group'
      ],
      inputContainer: [
        '.composer-parent',
        '.input-container',
        '.prompt-container'
      ]
    }
  }



  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = this.findElements(this.config.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []

      messageElements.forEach((element, index) => {
        const isUser = element.querySelector('[data-message-author-role="user"]') !== null
        const contentElement = element.querySelector('.markdown') || element.querySelector('[data-message-content]')

        if (contentElement) {
          const content = this.cleanContent(contentElement.textContent || '')
          if (content) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      // 使用配置的标题选择器
      const titleElement = this.findElement(this.config.selectors.conversationTitle || ['h1'])
      const title = titleElement.element?.textContent || `ChatGPT对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `chatgpt-${Date.now()}`,
        platform: 'chatgpt',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('【EchoSync】Extract ChatGPT conversation error:', error)
      return null
    }
  }

  /**
   * 提取最新的AI回答
   */
  protected async extractLatestAnswer(): Promise<string | null> {
    try {
      // 使用配置的助手消息选择器
      const answerElements = this.findElements([
        '.markdown',
        '.prose',
        '[data-message-content]',
        ...(this.config.selectors.assistantMessage || [])
      ])

      if (answerElements.length === 0) return null

      // 获取最后一个答案（排除用户消息）
      for (let i = answerElements.length - 1; i >= 0; i--) {
        const element = answerElements[i]
        const isUserMessage = element.closest('[data-message-author-role="user"]') !== null

        if (!isUserMessage) {
          const content = this.cleanContent(element.textContent || '')
          if (content.length > 0) {
            console.log('【EchoSync】ChatGPT extracted answer:', content.substring(0, 100) + '...')
            return content
          }
        }
      }

      return null
    } catch (error) {
      console.error('【EchoSync】Extract ChatGPT latest answer error:', error)
      return null
    }
  }
}
